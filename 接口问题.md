# 接口问题记录

| 问题描述 | 相关文件 | 影响范围 | 解决方案/备注 |
|---|---|---|---|
| 新接口 `/coupons/list` 缺少 `quantity` 字段，无法展示优惠券总数量。 | `src/views/coupon/index.vue` | 优惠券列表页 | 暂无直接对应字段，如果需要显示总数量，可能需要后端补充或前端根据其他逻辑推算。 |
| 新接口 `/coupons/list` 缺少 `createTime` 字段，无法展示优惠券创建时间。 | `src/views/coupon/index.vue` | 优惠券列表页 | 暂无直接对应字段，已移除前端表格中的“创建时间”列。 |
| 新接口 `/coupons/list` `remainingQuantity` 字段变更为 `receiveNum` (用户领取数量)，与旧字段含义不完全一致。 | `src/views/coupon/index.vue` | 优惠券列表页 | 已将表格列改为“领取数量”，并映射到 `receiveNum`。 |
| `/coupons/receiveStatus` 接口文档问题 | `src/api/system/coupon.js` | 领取情况弹窗 | 接口文档指明请求方式为 `GET`，但参数 `couponsId` 和 `status` 却被标记为 `body` 参数。`GET` 请求通常不包含 `body`。已在代码实现中假设参数通过 `query` 传递。 |
| `/post/list` 接口文档问题 | `src/api/post.js` | 文章管理列表 | 接口文档指明请求方式为 `POST`，但参数却被标记为 `query` 类型。已在代码实现中将参数作为 `data` (body) 发送。 |
| `/post/list` 接口缺少文章详情接口 | `src/views/article/index.vue` | 文章管理列表 | 缺少获取单篇文章详情的接口，编辑和预览功能无法获取最新数据。 |
| `/post/publish` 和 `/post/update` 接口缺少字段 | `src/views/article/ArticleForm.vue` | 文章发布/编辑 | 接口需要 `communityId` 和 `scopeRange`，但表单中未提供相应输入项。 |
| `/member/query` 接口缺少搜索字段 | `src/views/member/index.vue` | 会员管理列表 | 接口请求参数中只有 `pageNum` 和 `pageSize`，缺少按用户昵称搜索的字段。前端搜索功能已暂时注释。 |
| `/member/query` 接口缺少会员详情相关字段 | `src/views/member/index.vue` | 会员详情弹窗 | 接口返回的 `MemberUserVO` 中缺少详情弹窗需要的字段：消费平台优惠券数量、领取优惠券数量、消费当前商家优惠券数量、关联商家会员数量、消费范围距离等。目前使用模拟数据。 |
| `/goods/list` 接口缺少浏览数量 | `src/views/commodity/index.vue` | 商品列表页 | 接口返回数据中缺少 `viewCount` 字段，前端无法展示浏览数量。 |
| `/goods/list` 接口缺少创建时间 | `src/views/commodity/index.vue` | 商品列表页 | 接口返回数据中缺少 `createTime` 字段，前端无法展示创建时间。 |
| 缺少删除商品接口 | `src/views/commodity/index.vue` | 商品列表页 | 缺少用于删除商品的接口。 |
| 缺少获取单个商品详情接口 | `src/views/commodity/index.vue` | 商品编辑/预览 | 缺少用于获取单个商品详细信息的接口，影响编辑和预览功能。 |