<template>
  <canvas :id="canvasId" @click="changeCode" style="cursor: pointer"></canvas>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";

/**
 * @description: 永不重复的ID
 */
function GenNonDuplicateID() {
  let str: any = "";
  str = Math.random().toString(36).substr(3);
  str += Date.now().toString(16).substr(4);
  return str;
}
const props = defineProps({
  // 宽度
  width: {
    type: [String, Number],
    default: "100%",
  },
  // 高度
  height: {
    type: [String, Number],
    default: "100%",
  },
  //背景颜色
  bgColor: {
    type: String,
    default: "#f5f7fa",
  },
  //验证码生成规则范围
  codeRange: {
    type: Array,
    default: () => [],
  },
  //验证码位数
  codeLen: {
    type: Number,
    default: 4,
  },
  //验证码的值
  code: {
    type: String,
    default: "",
  },
  //透明度
  globalAlpha: {
    type: Number,
    default: 1,
  },
});

const emits = defineEmits({
  change: null,
  click: null,
});

let canvasId: string = "NsVerificationCode-" + GenNonDuplicateID(); //画布Id
let ctx: any = null; //画布
const codeValue = ref<string>(""); //验证码的值
let canvasWidth = 0; //画布宽度
let canvasHeight = 0; //画布高度

//点击得更新画布内容
function changeCode(e: any) {
  if (!props.code) {
    //用户没有传进来值，自己生成
    createCode();
  }
  emits("click", e);
}

//创建画布
onMounted(() => {
  //获取生成二维码的dom元素
  const canvas: any = document.querySelector("#" + canvasId);
  //创建画布
  if (typeof props.width === "number") {
    canvasWidth = Number(props.width);
  } else {
    if (props.width.includes("%")) {
      const num = Number(props.width.replace("%", ""));
      canvasWidth = (canvas?.parentNode?.clientWidth * num) / 100;
    } else {
      canvasWidth = parseFloat(props.width);
    }
  }
  if (typeof props.height === "number") {
    canvasHeight = Number(props.height);
  } else {
    if (props.height.includes("%")) {
      const num = Number(props.height.replace("%", ""));
      canvasHeight = (canvas?.parentNode?.clientHeight * num) / 100;
    } else {
      canvasHeight = parseFloat(props.height);
    }
  }
  //赋值
  ctx = canvas.getContext("2d");
  //设置画布宽高
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;
  //生成验证码
  createCode();
});

//监听动态变更
watch(
  () => [props.code, props.bgColor, props.codeLen, props.globalAlpha],
  () => {
    createCode();
  }
);

//生成验证码
function createCode() {
  //清理画布
  clearCanvas();
  //判断用户是否自带了code
  let code = props.code;
  if (!code) {
    code = randomCode();
  }
  codeValue.value = code;
  //完成背景、干扰线、干扰点、验证码值绘画
  drawOther(code);
  //事件传递，值改变
  emits("change", code);
}

//完成背景、干扰线、干扰点、验证码值绘画
function drawOther(code: string) {
  drawBackground();
  drawDisturbLines();
  drawDisturbDots();
  drawLetters(code);
}

//清理画布
function clearCanvas() {
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);
  ctx.globalAlpha = 1;
}

//获取生成二维码的规则范围
function generateCodeRange() {
  const codeRange: any = [];
  for (let i = "A".charCodeAt(0); i <= "Z".charCodeAt(0); i++) {
    codeRange.push(String.fromCharCode(i));
  }
  for (let i = "a".charCodeAt(0); i <= "z".charCodeAt(0); i++) {
    codeRange.push(String.fromCharCode(i));
  }
  for (let i = "0".charCodeAt(0); i <= "9".charCodeAt(0); i++) {
    codeRange.push(String.fromCharCode(i));
  }
  return codeRange;
}

// 生成四位随机数
function randomCode() {
  let code = "";
  let codeRange: any = [];
  if (props.codeRange.length > 0) {
    //如果用户传了规则，就用用户的规则
    codeRange = props.codeRange;
  } else {
    codeRange = generateCodeRange();
  }
  let len = codeRange.length;
  for (let i = 0; i < props.codeLen; i++) {
    code += codeRange[Math.floor(Math.random() * len)];
  }
  return code;
}

// 画背景色
function drawBackground() {
  ctx.rect(0, 0, canvasWidth, canvasHeight);
  ctx.fillStyle = props.bgColor;
  ctx.fill();
}

// 画干扰线
function drawDisturbLines() {
  const drawOneLine = () => {
    const startX = Math.floor(Math.random() * canvasWidth);
    const startY = Math.floor(Math.random() * canvasHeight);
    const endX = Math.floor(Math.random() * canvasWidth);
    const endY = Math.floor(Math.random() * canvasHeight);
    ctx.strokeStyle = randomColor();
    ctx.lineWidth = Math.ceil(Math.random() * 2);
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.closePath();
    ctx.stroke();
  };
  for (let i = 0; i < 4; i++) {
    drawOneLine();
  }
}

// 画干扰点
function drawDisturbDots() {
  const drawOneDot = () => {
    const ox = Math.floor(Math.random() * canvasWidth);
    const oy = Math.floor(Math.random() * canvasHeight);
    ctx.fillStyle = randomColor();
    ctx.beginPath();
    ctx.arc(ox, oy, 1, 0, 2 * Math.PI);
    ctx.closePath();
    ctx.fill();
  };
  for (let i = 0, count = (canvasWidth * canvasHeight) / 100; i < count; i++) {
    drawOneDot();
  }
}

// 画文字(数字或字母)
function drawLetters(defaultcode: string) {
  const code = defaultcode.split("");
  const drawOneLetter = (letter, x, y, fontSize) => {
    ctx.font = fontSize + "px Times new roman";
    ctx.textBaseline = "middle";
    ctx.globalAlpha = props.globalAlpha;
    ctx.fillStyle = randomColor();
    ctx.fillText(letter, x, y);
  };
  for (let i = 0, len = code.length; i < len; i++) {
    const offsetX = canvasWidth * 0.15; // 中间的70%画字母，两边各15%
    const x = (i * canvasWidth * 0.7) / code.length + offsetX;
    const y = canvasHeight / 2;
    const letter = code[i];
    let bl = 0.7;
    bl = bl - (code.length - 4) * 0.1;
    if (/.*[\u4e00-\u9fa5]+.*$/.test(letter)) {
      bl = bl - 0.4;
    }
    bl = bl < 0.1 ? 0.1 : bl;
    const fontSize = Math.min(canvasHeight * bl, canvasWidth * bl);
    drawOneLetter(letter, x, y, fontSize);
  }
}

// 生成随机色
function randomColor() {
  let colorStr = "#";
  for (let i = 0; i < 6; i++) {
    colorStr += Math.floor(Math.random() * 16).toString(16);
  }
  return colorStr;
}

defineExpose({
  canvasId,
  codeValue,
  changeCode, // 暴露 changeCode 方法
});
</script>
<script lang="ts">
export default {
  name: "VerificationCode",
};
</script>
