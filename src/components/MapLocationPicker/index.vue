<template>
  <div class="map-location-picker">
    <!-- 地址搜索输入框 -->
    <div class="search-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索地址或地点"
        clearable
        @input="handleSearchInput"
        @clear="handleSearchClear"
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- 搜索建议列表 -->
      <div v-if="searchSuggestions.length > 0" class="suggestions-list">
        <div
          v-for="(suggestion, index) in searchSuggestions"
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(suggestion)"
        >
          <div class="suggestion-title">{{ suggestion.title }}</div>
          <div class="suggestion-address">{{ suggestion.address }}</div>
        </div>
      </div>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
      <tlbs-map
        ref="mapRef"
        :api-key="apiKey"
        :center="mapCenter"
        :zoom="mapZoom"
        @click="onMapClick"
        @map_inited="onMapInit"
        @dragend="onMapDragEnd"
      >
      </tlbs-map>

      <!-- 固定在地图中心的标记点 -->
      <div class="center-marker">
        <div class="marker-pin"></div>
        <div class="marker-shadow"></div>
      </div>

      <!-- 地图加载状态 -->
      <div v-if="mapLoading" class="map-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>地图加载中...</span>
      </div>
    </div>

    <!-- 位置信息显示 -->
    <div class="location-info">
      <div class="info-row">
        <span class="info-label">位置信息：</span>
        <span class="info-value">{{
          mapAddress || "请在地图上选择位置"
        }}</span>
      </div>
      <div class="info-row address-input-row">
        <span class="info-label">门牌号：</span>
        <el-input
          v-model="houseNumber"
          placeholder="请输入门牌号"
          size="small"
          class="house-number-input"
          @input="updateFullAddress"
        />
      </div>
      <div class="info-row">
        <span class="info-label">完整地址：</span>
        <span class="info-value">{{
          fullAddress || "请选择位置并输入门牌号"
        }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">经纬度：</span>
        <span class="info-value">
          {{
            currentLongitude && currentLatitude
              ? `${currentLongitude.toFixed(6)}, ${currentLatitude.toFixed(6)}`
              : "未选择"
          }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { Search, Loading } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  longitude: {
    type: Number,
    default: null,
  },
  latitude: {
    type: Number,
    default: null,
  },
  height: {
    type: String,
    default: "300px",
  },
  zoom: {
    type: Number,
    default: 17,
  },
});

const emit = defineEmits([
  "update:modelValue",
  "update:longitude",
  "update:latitude",
  "locationChange",
]);

// 腾讯地图API Key
const apiKey = "4KCBZ-RCMKW-DPQRK-3ZEEO-BUJUK-WJF6K";

// 响应式数据
const mapLoading = ref(true);
const searchKeyword = ref("");
const searchSuggestions = ref([]);
const mapAddress = ref(""); // 地图获取的位置信息
const houseNumber = ref(""); // 用户输入的门牌号
const fullAddress = ref(props.modelValue); // 完整地址
const currentLongitude = ref(props.longitude);
const currentLatitude = ref(props.latitude);

// 地图相关变量
let map = null;
let searchTimeout = null;
let dragEndTimeout = null;

// 默认中心点（北京天安门）
const defaultCenter = { lat: 39.91799, lng: 116.397027 };

// 计算地图中心点
const mapCenter = computed(() => {
  if (props.longitude && props.latitude) {
    console.log(55555, props.longitude, props.latitude);

    return { lat: props.latitude, lng: props.longitude };
  }
  return defaultCenter;
});

// 地图缩放级别
const mapZoom = computed(() => props.zoom);


// 地图初始化完成
const onMapInit = (mapInstance) => {
  console.log("地图初始化完成");
  map = mapInstance;
  mapLoading.value = false;
};

// 地图点击事件
const onMapClick = (event) => {
  const { lat, lng } = event.latLng;
  updateLocation(lng, lat);
};

// 地图拖拽结束事件 - 使用节流
const onMapDragEnd = () => {
  if (dragEndTimeout) {
    clearTimeout(dragEndTimeout);
  }
  
  dragEndTimeout = setTimeout(() => {
    if (map) {
      const center = map.getCenter();
      const lng = center.lng;
      const lat = center.lat;
      updateLocation(lng, lat);
    }
  }, 500); // 500ms 节流
};

// 更新位置信息
const updateLocation = async (lng, lat) => {
  try {
    currentLongitude.value = lng;
    currentLatitude.value = lat;

    // 反向地理编码获取地址
    const address = await reverseGeocode(lng, lat);
    if (address) {
      mapAddress.value = address;
      updateFullAddress(); // 更新完整地址
    }

    // 发射事件
    emit("update:longitude", lng);
    emit("update:latitude", lat);
    emit("locationChange", {
      address: fullAddress.value,
      longitude: lng,
      latitude: lat,
    });
  } catch (error) {
    console.error("更新位置失败:", error);
    ElMessage.error("获取地址信息失败");
  }
};

// 反向地理编码 - 使用腾讯地图Web Service API
const reverseGeocode = (lng, lat) => {
  return new Promise((resolve, reject) => {
    // 创建JSONP回调函数名
    const callbackName = 'geocodeCallback_' + Date.now();
    
    // 创建全局回调函数
    window[callbackName] = (data) => {
      // 清理
      document.head.removeChild(script);
      delete window[callbackName];
      
      if (data.status === 0 && data.result) {
        const address = data.result.formatted_addresses?.recommend ||
                       data.result.address ||
                       `经度: ${lng.toFixed(6)}, 纬度: ${lat.toFixed(6)}`;
        resolve(address);
      } else {
        resolve(`经度: ${lng.toFixed(6)}, 纬度: ${lat.toFixed(6)}`);
      }
    };
    
    // 创建script标签进行JSONP请求
    const script = document.createElement('script');
    script.src = `https://apis.map.qq.com/ws/geocoder/v1/?key=${apiKey}&location=${lat},${lng}&output=jsonp&callback=${callbackName}`;
    script.onerror = () => {
      document.head.removeChild(script);
      delete window[callbackName];
      console.error('地址解析失败');
      resolve(`经度: ${lng.toFixed(6)}, 纬度: ${lat.toFixed(6)}`);
    };
    
    document.head.appendChild(script);
  });
};

// 搜索输入处理
const handleSearchInput = (value) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  if (!value.trim()) {
    searchSuggestions.value = [];
    return;
  }

  searchTimeout = setTimeout(() => {
    searchLocation(value);
  }, 300);
};

// 搜索地点 - 使用腾讯地图Web Service API
const searchLocation = (keyword) => {
  if (!keyword.trim()) {
    return;
  }

  // 创建JSONP回调函数名
  const callbackName = 'searchCallback_' + Date.now();
  
  // 创建全局回调函数
  window[callbackName] = (data) => {
    // 清理
    document.head.removeChild(script);
    delete window[callbackName];
    
    if (data.status === 0 && data.data) {
      searchSuggestions.value = data.data.slice(0, 5).map((item) => ({
        title: item.title,
        address: item.address,
        location: item.location,
      }));
    } else {
      searchSuggestions.value = [];
    }
  };
  
  // 创建script标签进行JSONP请求
  const script = document.createElement('script');
  script.src = `https://apis.map.qq.com/ws/place/v1/suggestion?key=${apiKey}&keyword=${encodeURIComponent(keyword)}&region=全国&output=jsonp&callback=${callbackName}`;
  script.onerror = () => {
    document.head.removeChild(script);
    delete window[callbackName];
    console.error('搜索请求失败');
    searchSuggestions.value = [];
  };
  
  document.head.appendChild(script);
};

// 选择搜索建议
const selectSuggestion = (suggestion) => {
  searchKeyword.value = suggestion.title;
  searchSuggestions.value = [];

  if (suggestion.location) {
    const lng = suggestion.location.lng;
    const lat = suggestion.location.lat;

    // 更新位置
    updateLocation(lng, lat);
  }
};

// 清除搜索
const handleSearchClear = () => {
  searchSuggestions.value = [];
};

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== fullAddress.value) {
      parseInitialAddress(newVal);
    }
  }
);

watch(
  () => [props.longitude, props.latitude],
  ([newLng, newLat]) => {
    if (newLng !== currentLongitude.value || newLat !== currentLatitude.value) {
      currentLongitude.value = newLng;
      currentLatitude.value = newLat;
    }
  }
);

// 获取IP位置
const getLocationByIP = () => {
  return new Promise((resolve, reject) => {
    // 创建JSONP回调函数名
    const callbackName = "ipLocationCallback_" + Date.now();

    // 创建全局回调函数
    window[callbackName] = (data) => {
      // 清理
      document.head.removeChild(script);
      delete window[callbackName];

      if (data.status === 0 && data.result) {
        resolve(data.result);
      } else {
        reject(new Error(data.message || "IP定位失败"));
      }
    };

    // 创建script标签进行JSONP请求
    const script = document.createElement("script");
    script.src = `https://apis.map.qq.com/ws/location/v1/ip?key=${apiKey}&output=jsonp&callback=${callbackName}`;
    script.onerror = () => {
      document.head.removeChild(script);
      delete window[callbackName];
      reject(new Error("网络请求失败"));
    };

    document.head.appendChild(script);
  });
};

// 检查是否需要IP定位
const checkAndGetIPLocation = async () => {
  // 检查经纬度是否为空或等于1.0
  const needIPLocation =
    !props.longitude ||
    !props.latitude ||
    props.longitude === 1.0 ||
    props.latitude === 1.0;

  if (needIPLocation) {
    try {
      console.log("开始获取IP位置...");
      const ipLocationData = await getLocationByIP();

      if (ipLocationData.location) {
        const { lng, lat } = ipLocationData.location;
        console.log("IP定位成功:", lng, lat);

        // 更新位置信息
        await updateLocation(lng, lat);

        ElMessage.success("已根据IP自动定位到当前位置");
      }
    } catch (error) {
      console.error("IP定位失败:", error);
      ElMessage.warning("IP定位失败，使用默认位置");
    }
  }
};

// 更新完整地址
const updateFullAddress = () => {
  if (mapAddress.value && houseNumber.value) {
    fullAddress.value = `${mapAddress.value} ${houseNumber.value}`;
  } else if (mapAddress.value) {
    fullAddress.value = mapAddress.value;
  } else {
    fullAddress.value = "";
  }
  
  // 发射完整地址更新事件
  emit("update:modelValue", fullAddress.value);
};

// 解析初始地址
const parseInitialAddress = (address) => {
  if (!address) return;
  
  // 简单的门牌号提取逻辑：假设门牌号在地址末尾，包含数字
  const parts = address.trim().split(/\s+/);
  const lastPart = parts[parts.length - 1];
  
  // 如果最后一部分包含数字，可能是门牌号
  if (/\d/.test(lastPart) && lastPart.length <= 10) {
    houseNumber.value = lastPart;
    mapAddress.value = parts.slice(0, -1).join(' ');
  } else {
    mapAddress.value = address;
  }
  
  fullAddress.value = address;
};

// 组件挂载时的初始化
onMounted(async () => {
  if (props.modelValue) {
    parseInitialAddress(props.modelValue);
  }
  if (props.longitude && props.latitude) {
    currentLongitude.value = props.longitude;
    currentLatitude.value = props.latitude;
  }

  // 检查并执行IP定位
  await checkAndGetIPLocation();
});
</script>

<style lang="scss" scoped>
.map-location-picker {
  width: 100%;

  .search-container {
    position: relative;
    margin-bottom: 10px;

    .search-input {
      width: 100%;
    }

    .suggestions-list {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      z-index: 9999;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 2px;

      .suggestion-item {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:hover {
          background-color: var(--el-fill-color-light);
        }

        &:last-child {
          border-bottom: none;
        }

        .suggestion-title {
          font-size: 14px;
          color: var(--el-text-color-primary);
          margin-bottom: 2px;
        }

        .suggestion-address {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .map-container {
    position: relative;
    height: v-bind(height);
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    overflow: hidden;

    .center-marker {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -100%);
      z-index: 1000;
      pointer-events: none;

      .marker-pin {
        width: 20px;
        height: 20px;
        background: #ff4444;
        border-radius: 50% 50% 50% 0;
        transform: rotate(-45deg);
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 8px;
          height: 8px;
          background: white;
          border-radius: 50%;
          transform: translate(-50%, -50%) rotate(45deg);
        }
      }

      .marker-shadow {
        width: 20px;
        height: 6px;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 50%;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .map-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;

      .el-icon {
        font-size: 24px;
        margin-bottom: 8px;
        color: var(--el-color-primary);
      }

      span {
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .location-info {
    margin-top: 10px;
    padding: 12px;
    background: var(--el-fill-color-lighter);
    border-radius: 4px;

    .info-row {
      display: flex;
      margin-bottom: 6px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 13px;
        color: var(--el-text-color-secondary);
        width: 80px;
        flex-shrink: 0;
      }

      .info-value {
        font-size: 13px;
        color: var(--el-text-color-primary);
        flex: 1;
        word-break: break-all;
      }

      &.address-input-row {
        .house-number-input {
          flex: 1;
          
          :deep(.el-input__wrapper) {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
