<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="''"
      list-type="picture-card"
      :http-request="handleCustomUpload"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :before-remove="handleDelete"
      :show-file-list="true"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon"><plus /></el-icon>
    </el-upload>
    <!-- 上传提示 -->
    <slot name="tip">
      <div class="el-upload__tip" v-if="showTip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </slot>

    <el-dialog
      v-model="dialogVisible"
      title="预览"
      width="800px"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { isExternal } from "@/utils/validate";
import { uploadFileToCos } from "@/utils/cosUpload";
import { nextTick } from "vue";

const props = defineProps({
  modelValue: [String, Object, Array],
  // 上传接口地址
  action: {
    type: String,
    default: "/common/upload",
  },
  // 上传携带的参数
  data: {
    type: Object,
  },
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  // 是否需要加入尺寸
  needAddSize: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",");
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          if (item.indexOf(baseUrl) === -1 && !isExternal(item)) {
            item = { name: baseUrl + item, url: baseUrl + item };
          } else {
            item = { name: item, url: item };
          }
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some((type) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(
      `文件格式不正确，请上传${props.fileType.join("/")}图片格式文件!`
    );
    return false;
  }
  if (file.name.includes(",")) {
    proxy.$modal.msgError("文件名不正确，不能包含英文逗号!");
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  return true;
}

// 自定义上传函数
async function handleCustomUpload(options) {
  const { file } = options;

  // 先进行上传前验证
  if (!handleBeforeUpload(file)) {
    return;
  }

  proxy.$modal.loading("正在上传图片，请稍候...");
  number.value++;

  try {
    let newfile = file;
    if (props.needAddSize) {
      const { width, height } = await getImageWidthAndHeight(file);
      const newName = file.name + `/size=${width}x${height}`;
      newfile = new File([file], newName, {
        type: file.type,
        lastModified: file.lastModified,
      });
    }
    // 使用腾讯云COS上传
    const fileUrl = await uploadFileToCos(newfile, (progress) => {
      // 可以在这里处理上传进度
      console.log(`上传进度: ${progress.percent}%`);
    });
    

    // 上传成功，添加到上传列表
    uploadList.value.push({
      name: fileUrl,
      url: fileUrl,
    });
    uploadedSuccessfully();
  } catch (error) {
    console.error("上传失败:", error);
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(error.message || "上传图片失败");

    // 上传失败时，清空失败的图片预览
    handleUploadFailure(file);
  }
}

function getImageWidthAndHeight(file) {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    img.onerror = () => {
      reject(new Error("图片加载失败"));
    };

    // 创建文件URL
    img.src = URL.createObjectURL(file);
  });
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    emit("update:modelValue", listToString(fileList.value));
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit("update:modelValue", listToString(fileList.value));
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传图片失败");
  proxy.$modal.closeLoading();
}

// 处理上传失败，清空失败的图片
function handleUploadFailure(failedFile) {
  // 从fileList中移除失败的文件
  // Element Plus会自动添加选择的文件到显示列表，我们需要手动移除失败的文件
  nextTick(() => {
    // 获取upload组件的内部文件列表
    const uploadComponent = proxy.$refs.imageUpload;
    if (uploadComponent && uploadComponent.uploadFiles) {
      // 找到失败的文件并移除
      const failedFileIndex = uploadComponent.uploadFiles.findIndex(
        (file) => file.name === failedFile.name && file.raw === failedFile
      );

      if (failedFileIndex > -1) {
        // 移除失败的文件
        uploadComponent.uploadFiles.splice(failedFileIndex, 1);

        // 同时从我们的fileList中移除（如果存在）
        const ourFileIndex = fileList.value.findIndex(
          (file) =>
            file.name === failedFile.name &&
            (!file.url || file.url.startsWith("blob:"))
        );

        if (ourFileIndex > -1) {
          fileList.value.splice(ourFileIndex, 1);
          // 更新父组件的值
          emit("update:modelValue", listToString(fileList.value));
        }
      }
    }
  });
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substring(0, strs.length - 1) : "";
}
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
</style>
