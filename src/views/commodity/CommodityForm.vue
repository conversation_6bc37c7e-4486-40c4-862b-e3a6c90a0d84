<template>
  <el-dialog
    :title="title"
    v-model="visible"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    @close="handleClose"
    custom-class="commodity-dialog"
    :fullscreen="isMobile"
  >
    <!-- 双重标题 -->
    <template #title>
      <div class="dialog-title">
        <span v-if="isPreview">商品详情</span>
        <span v-else-if="isEdit">编辑商品</span>
        <span v-else>新增商品</span>
      </div>
    </template>

    <el-alert v-if="!isPreview"
      title="注意事项：请确保商品信息真实有效，符合平台规定。"
      type="warning"
      show-icon
      :closable="false"
      class="mb10"
    />

    <el-form
      ref="commodityFormRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      label-position="top"
      :disabled="isPreview"
    >
      <el-row :gutter="isMobile ? 0 : 20">
        <!-- 左侧表单项 -->
        <el-col :span="isMobile ? 24 : 10">
          <el-form-item label="封面图" prop="coverUrl">
             <image-upload
                v-model="form.coverImage"
                :limit="1"
                :fileSize="5"
                :fileType="['png', 'jpg', 'jpeg']"
                :isShowTip="false"
              />
          </el-form-item>

          <el-form-item label="商品相册" prop="images">
            <image-upload
              v-model="form.images"
              :limit="3"
              :fileType="['png', 'jpg', 'jpeg']"
              :isShowTip="false"
              class="image-uploader"
              :disabled="isPreview"
            />
          </el-form-item>

          <el-form-item label="类型" prop="typeId">
            <el-select
              v-model="form.typeId"
              placeholder="请选择类型"
              class="select-with-arrow"
            >
              <!-- 类型选项，后续动态加载 -->
              <el-option label="普通商品" :value="1"></el-option>
              <el-option label="优惠商品" :value="3"></el-option>
              <template #append>
                <el-icon><ArrowRight /></el-icon>
              </template>
            </el-select>
          </el-form-item>

          <el-form-item label="价格" prop="price">
            <el-input
              v-model="form.price"
              placeholder="0.00"
              @input="handlePriceAmountInput"
              :precision="2"
              :formatter="
                (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              "
              :parser="(value) => value.replace(/,/g, '')"
            >
              <template #append>元</template>
            </el-input>
          </el-form-item>

          <!-- 根据商品类型显示不同的字段 -->
          <!-- 普通商品：无额外字段 -->

          <!-- 拼团商品：成团人数 -->
          <el-form-item v-if="form.typeId === 2" label="成团人数" prop="numGroup">
            <el-input
              v-model="form.numGroup"
              placeholder="30"
              @input="handleQuantityInput"
            >
              <template #append>人</template>
            </el-input>
            <div class="el-form-item__info" v-if="!isPreview">
              <el-icon><InfoFilled /></el-icon> 设置拼团所需的人数
            </div>
          </el-form-item>

          <!-- 优惠商品：优惠价格 -->
          <el-form-item v-if="form.typeId === 3" label="优惠价格" prop="discountPrice">
            <el-input
              v-model="form.discountPrice"
              placeholder="0.00"
              @input="handleDiscountAmountInput"
              :formatter="
                (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              "
              :parser="(value) => value.replace(/,/g, '')"
            >
              <template #append>元</template>
            </el-input>
            <div class="el-form-item__info" v-if="!isPreview">
              <el-icon><InfoFilled /></el-icon> 设置优惠后的价格
            </div>
          </el-form-item>

        </el-col>

        <!-- 右侧表单项 -->
        <el-col :span="isMobile ? 24 : 14">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入商品名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <!-- 考虑替换为富文本编辑器 -->
            <el-input
              type="textarea"
              :rows="isMobile ? 10 : 18"
              v-model="form.content"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="!isPreview" @click="cancel">取 消</el-button>
        <el-button v-if="!isPreview" type="primary" @click="submitForm">{{ isEdit ? '确认修改' : '确认发布' }}</el-button>
        <el-button v-else type="primary" @click="cancel">关 闭</el-button>
      </div>
    </template>

    <!-- 图片预览弹窗 (如果需要预览封面) -->
    <el-dialog
      v-model="previewVisible"
      title="封面预览"
      width="600px"
      append-to-body
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  computed,
  onMounted,
  onUnmounted
} from "vue";
// import { getToken } from "@/utils/auth"; // 获取token用于上传请求头 (暂时未使用)
import { ArrowRight, InfoFilled } from "@element-plus/icons-vue"; // 引入图标
import ImageUpload from "@/components/ImageUpload/index.vue";
import { addCommodity, updateCommodity } from "@/api/commodity"; // 假设的API路径

const props = defineProps({
  modelValue: { // 控制弹窗显示/隐藏
    type: Boolean,
    required: true,
  },
  commodityData: { // 新增：接收父组件传来的完整数据
    type: Object,
    default: () => ({})
  },
  // commodityId 可以保留，用于可能的 API 调用或标识，但填充表单优先用 commodityData
  commodityId: { // 编辑或预览时传入的商品ID (现在主要通过 commodityData 获取)
    type: [Number, String],
    default: null,
  },
  isEdit: { // 是否为编辑状态
    type: Boolean,
    default: false,
  },
  isPreview: { // 是否为预览模式
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(["update:modelValue", "submitSuccess"]);

const { proxy } = getCurrentInstance(); // 获取全局方法

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '900px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '5vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// --- 上传相关配置 ---
const coverFileList = ref([]); // 封面图文件列表
// const maxFileSize = 5; // 文件大小限制 (MB) (暂时未使用)
const previewVisible = ref(false); // 图片预览弹窗
const previewImageUrl = ref(""); // 预览图片URL

// --- 弹窗和表单状态 ---
const visible = ref(false);
const title = ref(""); // 弹窗标题 (通过 #title 插槽实现双标题)
const form = ref({});
const commodityFormRef = ref(null);

// 监听 modelValue 变化来控制内部 visible
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      // 弹窗打开时，根据状态设置标题和表单数据
      if (props.isPreview) {
        title.value = "商品详情";
      } else {
        title.value = props.isEdit ? "编辑商品" : "新增商品";
      }
      // 使用 nextTick 确保 DOM 更新后再重置表单和赋值
      nextTick(() => {
        reset(); // 先重置表单
        // 编辑模式下填充数据 (优先使用传入的 commodityData)
        if (props.isEdit && props.commodityData && Object.keys(props.commodityData).length > 0) {
            form.value = { ...props.commodityData }; // 使用传入的数据填充表单

            // 处理商品相册：将逗号分隔的字符串转换为数组
            if (form.value.images && typeof form.value.images === 'string') {
              form.value.images = form.value.images.split(',').filter(url => url.trim());
            } else if (!form.value.images) {
              form.value.images = [];
            }

            // 回显封面图
            coverFileList.value = form.value.coverImage
              ? [{ name: "cover", url: form.value.coverImage, status: 'success' }]
              : [];
        }
        // 预览模式下填充数据 (使用传入的 commodityData)
        else if (props.isPreview && props.commodityData && Object.keys(props.commodityData).length > 0) {
            form.value = { ...props.commodityData }; // 使用传入的数据填充表单

            // 处理商品相册：将逗号分隔的字符串转换为数组
            if (form.value.images && typeof form.value.images === 'string') {
              form.value.images = form.value.images.split(',').filter(url => url.trim());
            } else if (!form.value.images) {
              form.value.images = [];
            }

            // 回显封面图
            coverFileList.value = form.value.coverImage
              ? [{ name: "cover", url: form.value.coverImage, status: 'success' }]
              : [];
            // 预览模式下清除校验状态
            if (commodityFormRef.value) {
               commodityFormRef.value.clearValidate();
            }
        }
        // --- 保留原始预览逻辑的注释，以备将来需要API调用 ---
        // else if (props.isPreview && props.commodityId) {
          // // 这里应该调用API获取商品详情 (如果需要的话)
          // // getCommodityDetail(props.commodityId).then(response => {
          // //   form.value = response.data;
          //   // 回显封面图
          //   coverFileList.value = form.value.coverUrl
          //     ? [{ name: "cover", url: form.value.coverUrl, status: 'success' }]
          //     : [];
          //   // 预览模式下清除校验状态
          //   if (props.isPreview && commodityFormRef.value) {
          //      commodityFormRef.value.clearValidate();
          //   }
          // }).catch(() => {
          //    proxy.$modal.msgError("获取商品详情失败");
          // });

          // // --- 预览模式的模拟数据填充 (如果预览不依赖API) ---
          // const mockPreviewData = {
          //     commodityId: props.commodityId,
          //     name: '预览的商品名称',
          //     description: '<p>这是预览的商品描述</p>',
          //     coverUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          //     typeId: '1',
          //     price: 50.00,
          //     discountPrice: 36.00,
          // };
          // form.value = { ...mockPreviewData };
          // coverFileList.value = form.value.coverUrl
          //     ? [{ name: "cover", url: form.value.coverUrl, status: 'success' }]
          //     : [];
          // if (commodityFormRef.value) {
          //     commodityFormRef.value.clearValidate();
          // }
          // // --- 模拟数据填充结束 ---
        // }
        else {
          // 新增时清空文件列表
          coverFileList.value = [];
        }
      });
    }
  },
  { immediate: true }
);

// 表单校验规则
// 预览模式下不应用规则
const rules = computed(() => {
  if (props.isPreview) return {};

  const baseRules = {
    coverImage: [
      { required: true, message: "请上传封面图", trigger: "change" }
    ],
    name: [{ required: true, message: "商品名称不能为空", trigger: "blur" }],
    typeId: [{ required: true, message: "请选择商品类型", trigger: "change" }],
    price: [
      { required: true, message: "价格不能为空", trigger: "blur" },
      { type: 'number', message: '价格必须为数字值'}
    ],
  };

  // 根据商品类型添加条件校验
  if (form.value.typeId === 2) {
    // 拼团商品需要成团人数
    baseRules.numGroup = [
      { required: true, message: "请输入成团人数", trigger: "blur" }
    ];
  } else if (form.value.typeId === 3) {
    // 优惠商品需要优惠价格
    baseRules.discountPrice = [
      { required: true, message: "请输入优惠价格", trigger: "blur" }
    ];
  }

  return baseRules;
});

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    name: undefined,
    content: undefined,
    coverImage: undefined, // 封面图URL
    images: [], // 商品相册，初始化为空数组
    typeId: 1, // 默认为普通商品
    price: undefined,
    discountPrice: undefined,
    numGroup: undefined, // 成团人数
    // 其他字段...
  };
  coverFileList.value = []; // 清空封面文件列表
  if (commodityFormRef.value) {
    commodityFormRef.value.resetFields(); // 重置校验状态
    // 可能需要手动重置富文本编辑器的内容，如果 resetFields 不生效
    // form.value.description = ''; // 取消注释如果需要
  }
}

// 输入处理函数

// 成团人数输入处理（限制正整数）
function handleQuantityInput(value) {
  // 只允许输入正整数
  const numValue = parseInt(value);
  if (isNaN(numValue) || numValue <= 0) {
    form.value.numGroup = '';
  } else {
    form.value.numGroup = numValue;
  }
}

// 优惠价格输入处理（限制正数，小数点后两位）
function handleDiscountAmountInput(value) {
  // 移除非数字字符（除了小数点）
  let cleanValue = value.replace(/[^\d.]/g, '');

  // 确保只有一个小数点
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('');
  }

  // 限制小数点后两位
  if (parts[1] && parts[1].length > 2) {
    cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
  }

  // 确保是正数
  const numValue = parseFloat(cleanValue);
  if (isNaN(numValue) || numValue < 0) {
    form.value.discountPrice = '';
  } else {
    form.value.discountPrice = cleanValue;
  }
}


// 优惠价格输入处理（限制正数，小数点后两位）
function handlePriceAmountInput(value) {
  // 移除非数字字符（除了小数点）
  let cleanValue = value.replace(/[^\d.]/g, '');

  // 确保只有一个小数点
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('');
  }

  // 限制小数点后两位
  if (parts[1] && parts[1].length > 2) {
    cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
  }

  // 确保是正数
  const numValue = parseFloat(cleanValue);
  if (isNaN(numValue) || numValue < 0) {
    form.value.price = '';
  } else {
    form.value.price = parseFloat(cleanValue);
  }
}

// --- 上传处理函数 (暂时保留，可能在ImageUpload组件中使用) ---

// 上传前校验 (暂时未使用)
// const beforeUpload = file => {
//   const isLtSize = file.size / 1024 / 1024 < maxFileSize;
//   if (!isLtSize) {
//     proxy.$modal.msgError(`上传文件大小不能超过 ${maxFileSize}MB!`);
//   }
//   return isLtSize;
// };

// 封面上传成功 (暂时未使用)
// const handleCoverSuccess = (response, uploadFile) => {
//   if (response.code === 200) {
//     form.value.coverImage = response.data.url; // 保存URL
//     coverFileList.value = [uploadFile]; // 更新文件列表（确保只有一个）
//     // 手动触发封面图校验
//     if (commodityFormRef.value) {
//       commodityFormRef.value.validateField("coverImage");
//     }
//     proxy.$modal.msgSuccess("封面上传成功");
//   } else {
//     proxy.$modal.msgError(response.msg || "封面上传失败");
//     // 从列表中移除失败的文件
//     coverFileList.value = coverFileList.value.filter(
//       f => f.uid !== uploadFile.uid
//     );
//      // 上传失败时清空表单中的 URL 并重新校验
//     form.value.coverImage = undefined;
//     if (commodityFormRef.value) {
//       commodityFormRef.value.validateField("coverImage");
//     }
//   }
// };

// 封面移除 (暂时未使用)
// const handleCoverRemove = (uploadFile, uploadFiles) => {
//   form.value.coverImage = undefined;
//   coverFileList.value = [];
//   // 手动触发封面图校验
//   if (commodityFormRef.value) {
//     commodityFormRef.value.validateField("coverImage");
//   }
// };

// 封面图片预览 (暂时未使用)
// const handlePictureCardPreview = uploadFile => {
//   previewImageUrl.value = uploadFile.url;
//   previewVisible.value = true;
// };


// --- 弹窗控制 ---

// 取消按钮
function cancel() {
  visible.value = false;
  emit("update:modelValue", false); // 更新父组件的 v-model
}

// 弹窗关闭时的回调
function handleClose() {
  emit("update:modelValue", false); // 确保关闭时同步状态
}

// 提交按钮
function submitForm() {
  // 预览模式下不执行提交
  if (props.isPreview) {
     cancel(); // 直接关闭
     return;
   }

  commodityFormRef.value.validate(valid => {
    if (valid) {
      // 处理提交数据，将图片数组转换为逗号分隔的字符串
      const submitData = { ...form.value };

      // 处理商品相册：将数组转换为逗号分隔的字符串
      if (submitData.images && Array.isArray(submitData.images)) {
        submitData.images = submitData.images.join(',');
      } else if (!submitData.images) {
        submitData.images = '';
      }

      const apiCall = props.isEdit
        ? updateCommodity(submitData)
        : addCommodity(submitData)

      apiCall
        .then((res) => {
          if (res.code === 200) {
             proxy.$modal.msgSuccess(props.isEdit ? "修改成功" : "新增成功");
             visible.value = false;
             emit("update:modelValue", false); // 关闭弹窗
             emit("submitSuccess"); // 通知父组件提交成功，以便刷新列表
          } else {
             proxy.$modal.msgError(res.message || "操作失败");
          }
        })
        .catch((err) => {
          // 处理API调用或其他错误
          console.error("Submit error:", err);
          proxy.$modal.msgError("操作失败");
        });
    } else {
        console.log('表单校验失败!');
        // 可以添加滚动到第一个错误项的逻辑
        // scrollToErrorField();
        return false;
    }
  });
}

// 可选：滚动到第一个错误字段
// function scrollToErrorField() {
//   nextTick(() => {
//     const isError = document.querySelector('.is-error');
//     if (isError) {
//       isError.scrollIntoView({ behavior: 'smooth', block: 'center' });
//     }
//   });
// }

</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
}

// 模拟选择框右侧箭头样式
.select-with-arrow {
  width: 100%;
  :deep(.el-input__wrapper) {
    padding-right: 30px; /* 为图标留出空间 */
  }
  :deep(.el-input__suffix) {
    right: 5px;
    /* 隐藏默认箭头 */
    .el-select__caret {
      display: none;
    }
  }
  .el-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--el-input-icon-color, var(--el-text-color-placeholder));
    cursor: pointer; // 添加手型指针，模拟可点击
  }
}

// PC端上传组件样式
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 110px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

// 限制图片列表容器宽度，防止换行混乱
.image-uploader :deep(.el-upload-list--picture-card) {
  gap: 8px; /* 图片间距 */
}

// 调整表单项间距
.el-form-item {
  margin-bottom: 18px; // 调整表单项之间的垂直间距
}

// 优惠价格提示信息样式
.el-form-item__info {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    display: flex;
    align-items: center;
    .el-icon {
        margin-right: 4px;
    }
}

// 折扣输入框样式
.discount-input {
  width: 100%;
  .el-input {
    width: 100%;
  }
}

// 调整dialog footer的样式
.dialog-footer {
  text-align: right;
}

// 针对特定上传组件的提示文字样式
.cover-uploader .el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

// 移动端适配
@media (max-width: 768px) {
  // 移动端上传组件样式调整
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
    line-height: 88px;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 80px;
    height: 80px;
  }

  // 移动端表单项间距调整
  .el-form-item {
    margin-bottom: 15px;
  }

  // 移动端按钮样式
  .dialog-footer {
    text-align: center;
    padding: 15px 0;

    .el-button {
      width: 120px;
      margin: 0 10px;
    }
  }

  // 移动端提示信息样式
  .el-form-item__info {
    font-size: 11px;
    margin-top: 3px;
  }

  // 移动端图片上传列表布局优化
  .image-uploader :deep(.el-upload-list--picture-card) {
    gap: 5px;
  }

  // 移动端选择框样式调整
  .select-with-arrow {
    :deep(.el-input__wrapper) {
      padding-right: 25px;
    }
    .el-icon {
      right: 8px;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  :deep(.el-upload--picture-card) {
    width: 70px;
    height: 70px;
    line-height: 78px;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 70px;
    height: 70px;
  }

  .dialog-footer {
    .el-button {
      width: 100px;
      margin: 0 5px;
    }
  }

  .el-form-item__info {
    font-size: 10px;
  }
}
</style>

// 全局样式，用于修改 el-dialog 内部结构
<style lang="scss">
.commodity-dialog {
  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-right: 0; // 覆盖默认 margin
    padding-bottom: 10px;
  }
  .el-dialog__body {
    padding: 20px 25px; // 调整内容区域内边距
  }
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 15px 25px;
  }
  // 修复 InputNumber 控件按钮在某些情况下的样式问题
  .el-input-number .el-input__inner {
    text-align: left;
  }

  // 移动端全屏模式样式调整
  &.is-fullscreen {
    .el-dialog__header {
      padding: 15px 20px 10px;
    }
    .el-dialog__body {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    .el-dialog__footer {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
    }
  }
}

// 移动端特殊样式
@media (max-width: 768px) {
  .commodity-dialog {
    .el-dialog__header {
      padding: 12px 15px 8px;
    }
    .el-dialog__body {
      padding: 15px;
    }
    .el-dialog__footer {
      padding: 10px 15px;
    }
  }
}
</style>