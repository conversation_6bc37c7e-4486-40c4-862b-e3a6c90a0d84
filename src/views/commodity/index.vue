<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['cms:commodity:add']"
          >添加商品</el-button
        >
      </el-col>
    </el-row>

    <el-tabs v-model="queryParams.typeId" class="mb8">
      <el-tab-pane label="普通商品" :name="1"></el-tab-pane>
      <el-tab-pane label="优惠商品" :name="3"></el-tab-pane>
    </el-tabs>

    <el-table v-loading="loading" :data="commodityList">
      <el-table-column
        label="封面"
        align="center"
        prop="coverImage"
        width="100"
      >
        <template #default="scope">
          <!-- 封面图片展示 -->
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.coverImage || defaultCover"
            fit="cover"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column
        label="名称"
        align="left"
        prop="name"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handlePreview(scope.row)">{{
            scope.row.name
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center" prop="price" width="100">
        <template #default="scope">
          <span>{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="queryParams.typeId === 3"
        label="优惠价格"
        align="center"
        prop="discountPrice"
        width="100"
      >
        <template #default="scope">
          <span>{{ scope.row.discountPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">上架</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="warning"
            >下架</el-tag
          >
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createdAt"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handlePreview(scope.row)"
            >查看详情</el-button
          >
          <!-- 根据状态显示上架或下架按钮 -->
          <el-button
            v-if="scope.row.status === 1"
            link
            type="warning"
            @click="handleToggleStatus(scope.row)"
            v-hasPermi="['cms:commodity:status']"
          >
            下架
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            link
            type="success"
            @click="handleToggleStatus(scope.row)"
            v-hasPermi="['cms:commodity:status']"
          >
            上架
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cms:commodity:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cms:commodity:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 商品表单弹窗 -->
    <commodity-form
      v-model="dialogVisible"
      :commodity-data="currentCommodity"
      :is-edit="isEditMode"
      :is-preview="isPreviewMode"
      @submitSuccess="getList"
    />
  </div>
</template>

<script setup name="Commodity">
import { ref, reactive, onMounted, onActivated, watch, getCurrentInstance } from "vue";
import CommodityForm from "./CommodityForm.vue"; // 引入表单组件
import { listCommodity, updateCommodityStatus, deleteCommodity } from "@/api/commodity"; // 实际API路径
import defaultCover from "@/assets/images/profile.jpg"; // 假设的默认封面图路径, 需要确认或替换

const { proxy } = getCurrentInstance();
const { parseTime } = proxy; // 假设 parseTime 在全局挂载

// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 商品表格数据
const commodityList = ref([]);
// 弹窗相关状态
const dialogVisible = ref(false); // 控制弹窗显示
const isEditMode = ref(false); // 是否为编辑模式
const isPreviewMode = ref(false); // 新增：是否为预览模式
const currentCommodity = ref({}); // 当前操作（编辑/预览）的商品数据

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  typeId: 1, // 商品分类，默认为普通商品
  merchantId: undefined, // 商家ID，根据实际情况设置
});

/** 查询商品列表 */
function getList() {
  if(loading.value) return
  loading.value = true;
  listCommodity(queryParams)
    .then((response) => {
      if (response.code === 200) {
        commodityList.value = response.data.list;
        total.value = response.data.total;
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 新增按钮操作 */
function handleAdd() {
  isEditMode.value = false;
  isPreviewMode.value = false; // 确保不是预览模式
  currentCommodity.value = {}; // 清空当前商品数据
  dialogVisible.value = true; // 打开弹窗
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 实际应调用API: getCommodity(row.id).then(response => {
  //   currentCommodity.value = response.data;
  //   isEditMode.value = true;
  //   dialogVisible.value = true;
  // });
  // 模拟获取数据
  currentCommodity.value = { ...row }; // 填充当前商品数据
  isEditMode.value = true;
  isPreviewMode.value = false; // 确保不是预览模式
  dialogVisible.value = true; // 打开弹窗
}

/** 上/下架按钮操作 */
function handleToggleStatus(row) {
  const text = row.status === 1 ? "下架" : "上架";
  const newStatus = row.status === 1 ? 2 : 1;
  proxy.$modal
    .confirm(`确认要"${text}"商品"${row.name}"吗？`)
    .then(() => {
      return updateCommodityStatus({ id: row.id, status: newStatus });
    })
    .then(() => {
      getList(); // 重新加载列表
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(() => {
      // 可选：如果用户取消，恢复按钮状态或给出提示
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除商品名称为"' + row.name + '"的数据项？')
    .then(function () {
      return deleteCommodity(row.id);
    })
    .then((response) => {
      if (response.code === 200) {
        getList(); // 重新加载列表
        proxy.$modal.msgSuccess("删除成功");
      } else {
        proxy.$modal.msgError(response.message || "删除失败");
      }
    })
    .catch(() => {});
}

/** 预览按钮操作 */
function handlePreview(row) {
  currentCommodity.value = { ...row }; // 填充当前商品数据
  isEditMode.value = false; // 不是编辑模式
  isPreviewMode.value = true; // 设置为预览模式
  dialogVisible.value = true; // 打开弹窗
}

// 监听类型切换
watch(
  () => queryParams.typeId,
  () => {
    queryParams.pageNum = 1; // 重置页码
    getList(); // 重新获取数据
  }
);

onMounted(() => {
  getList(); // 组件挂载后获取列表数据
});

// keep-alive 组件激活时重新获取数据
onActivated(() => {
  getList(); // 每次进入页面都获取最新数据
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 10px;
}
.el-tabs {
  /* 如果需要调整tabs和table之间的间距，可以在这里添加样式 */
  margin-bottom: 15px; /* 增加tabs和table之间的间距 */
}
.el-table {
  /* 重新计算表格高度，因为增加了tabs的高度 */
  height: calc(100vh - 340px) !important;
}
</style>
