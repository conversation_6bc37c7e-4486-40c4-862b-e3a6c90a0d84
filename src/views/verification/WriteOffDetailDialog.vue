<template>
  <el-dialog title="核销详情" v-model="dialogOpen" width="500px" append-to-body>
    <el-form :model="form" label-width="100px">
      <el-form-item label="核销码:">
        {{ form.writeOffCode }}
      </el-form-item>
      <el-form-item label="类型:">
        {{ getTypeText(form.type) }}
      </el-form-item>
      <el-form-item label="标题:">
        {{ form.subtitle }}
      </el-form-item>
      <el-form-item label="原价:" v-if="form.costPrice">
        ¥{{ form.costPrice }}
      </el-form-item>
      <el-form-item label="折扣价:" v-if="form.discountPrice">
        ¥{{ form.discountPrice }}
      </el-form-item>
      <el-form-item label="状态:">
        <el-tag :type="form.writeOffTime ? 'success' : 'warning'">
          {{ form.writeOffTime ? '已核销' : '未核销' }}
        </el-tag>
      </el-form-item>
      <el-form-item label="核销时间:" v-if="form.writeOffTime">
        {{ parseTime(form.writeOffTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button 
          v-if="!form.writeOffTime" 
          type="primary" 
          @click="submitForm"
        >
          确认核销
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';

const emit = defineEmits(['confirm']);

const dialogOpen = ref(false);
const form = ref({});

/** 获取类型文本 */
function getTypeText(type) {
  const typeMap = {
    1: '优惠券',
    2: '折扣券', 
    3: '团购'
  };
  return typeMap[type] || type;
}

/** 打开弹窗 */
function openDialog(data) {
  form.value = { ...data };
  dialogOpen.value = true;
}

/** 取消按钮 */
function cancel() {
  dialogOpen.value = false;
  form.value = {};
}

/** 提交按钮 */
async function submitForm() {
  try {
    await ElMessageBox.confirm('确认要核销此券码吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    // 发送核销确认事件
    emit('confirm', form.value.writeOffCode);
    cancel();
  } catch {
    // 用户取消操作
  }
}

defineExpose({
  open: openDialog,
});
</script>
