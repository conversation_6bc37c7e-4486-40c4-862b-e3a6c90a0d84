<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="search-bar">
        <el-input v-model="writeOffCode" placeholder="请输入核销码" style="width: 200px; margin-right: 10px;"></el-input>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="优惠券" name="1">
          <WriteOffList v-if="activeTab === '1'" ref="writeOffList1" :type="1" @open-detail="openDetailDialog" />
        </el-tab-pane>
        <el-tab-pane label="折扣券" name="2">
          <WriteOffList v-if="activeTab === '2'" ref="writeOffList2" :type="2" @open-detail="openDetailDialog" />
        </el-tab-pane>
        <!-- <el-tab-pane label="团购" name="3">
          <WriteOffList v-if="activeTab === '3'" ref="writeOffList3" :type="3" @open-detail="openDetailDialog" />
        </el-tab-pane> -->
      </el-tabs>
    </el-card>

    <WriteOffDetailDialog ref="detailDialog" @confirm="handleConfirmWriteOff" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import WriteOffList from './components/WriteOffList.vue';
import WriteOffDetailDialog from './WriteOffDetailDialog.vue';
import { searchWriteOff, confirmWriteOff } from '@/api/writeOff';

const writeOffCode = ref('');
const activeTab = ref('1');
const writeOffList1 = ref(null);
const writeOffList2 = ref(null);
const writeOffList3 = ref(null);
const detailDialog = ref(null);

/** 搜索核销码 */
async function handleSearch() {
  if (!writeOffCode.value.trim()) {
    ElMessage.warning('请输入核销码');
    return;
  }

  try {
    const response = await searchWriteOff(writeOffCode.value.trim());
    if (response.code === 0) {
      // 打开详情弹窗显示搜索结果
      detailDialog.value.open(response.data);
    } else {
      ElMessage.error(response.message || '搜索失败');
    }
  } catch (error) {
    ElMessage.error('搜索失败');
  }
}

/** 切换页签 */
function handleTabChange(name) {
  activeTab.value = name;
  writeOffCode.value = ''; // 清空核销码
  // 刷新当前页签的列表
  getCurrentWriteOffList()?.getList();
}

/** 打开详情弹窗 */
function openDetailDialog(row) {
  detailDialog.value.open(row);
}

/** 获取当前活跃的列表组件 */
function getCurrentWriteOffList() {
  switch (activeTab.value) {
    case '1':
      return writeOffList1.value;
    case '2':
      return writeOffList2.value;
    case '3':
      return writeOffList3.value;
    default:
      return null;
  }
}

/** 确认核销 */
async function handleConfirmWriteOff(writeOffCode) {
  try {
    const response = await confirmWriteOff({ writeOffCode });
    if (response.code === 0) {
      ElMessage.success('核销成功');
      // 刷新列表
      getCurrentWriteOffList()?.getList();
    } else {
      ElMessage.error(response.message || '核销失败');
    }
  } catch (error) {
    ElMessage.error('核销失败');
  }
}

onMounted(() => {
  // 页面加载时不需要自动查询，等用户切换页签或搜索
});
</script>

<style lang="scss" scoped>
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
</style>