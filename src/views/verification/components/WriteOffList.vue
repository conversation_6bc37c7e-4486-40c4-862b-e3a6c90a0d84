<template>
  <div>
    <el-table v-loading="loading" :data="writeOffList">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="核销码" align="center" prop="writeOffCode" />
      <el-table-column label="类型" align="center" prop="type">
        <template #default="scope">
          <span>{{ getTypeText(scope.row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="原价" align="center" prop="costPrice">
        <template #default="scope">
          <span>¥{{ scope.row.costPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="折扣价" align="center" prop="discountPrice">
        <template #default="scope">
          <span>¥{{ scope.row.discountPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="writeOffTime">
        <template #default="scope">
          <el-tag :type="scope.row.writeOffTime ? 'success' : 'warning'">
            {{ scope.row.writeOffTime ? '已核销' : '未核销' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="核销时间" align="center" prop="writeOffTime" width="180">
        <template #default="scope">
          <span>{{ scope.row.writeOffTime ? parseTime(scope.row.writeOffTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)">
            查看详情
          </el-button>
          <el-button 
            v-if="!scope.row.writeOffTime"
            link 
            type="success" 
            @click="handleWriteOff(scope.row)"
          >
            核销
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import { listWriteOff } from '@/api/writeOff';

const { proxy } = getCurrentInstance();

const props = defineProps({
  type: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['open-detail']);

const writeOffList = ref([]);
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: props.type,
});

/** 获取类型文本 */
function getTypeText(type) {
  const typeMap = {
    '1': '优惠券',
    '2': '折扣券', 
    '3': '团购'
  };
  return typeMap[type] || type;
}

/** 获取核销列表 */
async function getList() {
  loading.value = true;
  try {
    const response = await listWriteOff(queryParams);
    if (response.code === 0) {
      writeOffList.value = response.data.list || [];
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取核销列表失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 查看详情 */
function handleDetail(row) {
  emit('open-detail', row);
}

/** 核销操作 */
function handleWriteOff(row) {
  emit('open-detail', row);
}

// 监听type变化，重新获取数据
watch(() => props.type, () => {
  queryParams.type = props.type;
  queryParams.pageNum = 1;
  getList();
});

onMounted(() => {
  getList();
});

defineExpose({
  getList,
});
</script>
