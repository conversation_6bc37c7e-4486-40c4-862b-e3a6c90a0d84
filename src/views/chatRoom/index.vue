<template>
  <div
    class="chat-container"
    :class="{ 'mobile-layout': isMobile }"
    v-loading="isInitializing"
    element-loading-text="正在加载聊天系统..."
    element-loading-background="#fff"
  >
    <div class="container">
      <div class="header">
        <!-- 移动端菜单按钮 -->
        <div
          v-if="isMobile"
          class="mobile-menu-btn"
          @click="switchMobileView('menu')"
        >
          <i class="el-icon-menu"></i>
        </div>
        <!-- 移动端返回按钮 - 只在超小屏幕且聊天视图时显示 -->
        <div
          v-if="isMobile && currentMobileView === 'chat' && isVerySmallScreen"
          class="mobile-back-btn"
          @click="switchMobileView('list')"
        >
          <i class="el-icon-arrow-left"></i>
          <span>返回</span>
        </div>
        <div class="search" ref="searchRef" />
        <div class="add" ref="addRef" />
      </div>
      <div class="content">
        <!-- PC端布局 -->
        <template v-if="!isMobile">
          <div class="left">
            <div class="avatar-icon" ref="avatarRef" />
            <div
              class="chat-icon"
              :class="{
                active: model === 'chat',
              }"
              @click="() => (model = 'chat')"
            >
              <i
                class="iconfont icon-im"
                :class="{
                  iconfont: true,
                  'icon-im': true,
                }"
              />
              <div class="icon-label">会话</div>
            </div>
            <div class="logout-icon"></div>
          </div>
          <div class="right" v-show="model === 'chat'">
            <div class="right-list" ref="conversationRef" />
            <div class="right-content" ref="chatRef" />
          </div>
          <div class="right" v-show="model === 'contact'">
            <div class="right-list" ref="contactListRef" />
            <div class="right-content" ref="contactInfoRef" />
          </div>
          <div class="collect" v-if="model === 'collect'">
            <div class="collectRight" ref="collectRef"></div>
          </div>
        </template>

        <!-- 移动端布局 -->
        <template v-else>
          <!-- 移动端主内容区域 -->
          <div class="mobile-main">
            <!-- 移动端会话模式 -->
            <div class="mobile-chat-layout" v-show="model === 'chat'">
              <!-- 会话列表区域 -->
              <div
                class="mobile-conversation-panel"
                :class="{
                  collapsed: isVerySmallScreen && currentMobileView === 'chat',
                }"
              >
                <div class="right-list" ref="conversationRef" />
              </div>

              <!-- 聊天内容区域 -->
              <div
                class="mobile-chat-panel"
                :class="{
                  expanded: isVerySmallScreen && currentMobileView === 'chat',
                }"
              >
                <div class="right-content" ref="chatRef" />
              </div>
            </div>

            <!-- 移动端通讯录 -->
            <div
              class="mobile-contact"
              v-show="currentMobileView === 'list' && model === 'contact'"
            >
              <div class="right-list" ref="contactListRef" />
            </div>
            <div
              class="mobile-contact-info"
              v-show="currentMobileView === 'chat' && model === 'contact'"
            >
              <div class="right-content" ref="contactInfoRef" />
            </div>

            <!-- 移动端收藏 -->
            <div class="mobile-collect" v-if="model === 'collect'">
              <div class="collectRight" ref="collectRef"></div>
            </div>
          </div>

          <!-- 移动端侧边菜单遮罩 -->
          <div
            class="mobile-menu-overlay"
            v-show="showMobileMenu"
            @click="closeMobileMenu"
          ></div>

          <!-- 移动端侧边菜单 -->
          <div class="mobile-menu" :class="{ show: showMobileMenu }">
            <div class="mobile-menu-content">
              <div class="avatar-icon" ref="avatarRef" />
              <div
                class="chat-icon"
                :class="{
                  active: model === 'chat',
                }"
                @click="
                  () => {
                    model = 'chat';
                    switchMobileView('list');
                  }
                "
              >
                <i
                  class="iconfont icon-im"
                  :class="{
                    iconfont: true,
                    'icon-im': true,
                  }"
                />
                <div class="icon-label">会话</div>
              </div>
              <div class="logout-icon"></div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup name="ChatRoom">
import useUserStore from "@/store/modules/user";
import { IMUIKit } from "@xkit-yx/im-kit-ui";
import V2NIM from "nim-web-sdk-ng";
import { V2NIMConst } from "nim-web-sdk-ng/dist/esm/nim";
import { ref, onMounted, nextTick, onUnmounted, computed } from "vue";
import {
  ConversationContainer, // 会话列表组件
  ChatContainer, // 聊天（会话消息）组件
  AddContainer, // 搜索——添加按钮组件
  SearchContainer, // 搜索——搜索组件
  ContactListContainer, // 通讯录——通讯录导航组件
  ContactInfoContainer, // 通讯录——通讯录详情组件，包含好友列表、群组列表以及黑名单列表
  MyAvatarContainer, // 用户资料组件
  ChatCollectionList, // 收藏组件
} from "@xkit-yx/im-kit-ui";
import "@xkit-yx/im-kit-ui/es/style/css";
import "./iconfont.css";
import { compile } from "jsx-web-compiler";

//需要用到的 ref
const searchRef = ref();
const addRef = ref();
const avatarRef = ref();
const conversationRef = ref();
const chatRef = ref();
const contactListRef = ref();
const contactInfoRef = ref();
const collectRef = ref();
let uikit,
  uikitStore,
  initOptions = {};

const appkey = "ea407cc44e5ae4b02d6965502f2cb1aa"; //云信固定值
const isInitializing = ref(true);
const model = ref("chat");

// --- 响应式设计相关 ---
const isMobile = ref(false);
const showMobileMenu = ref(false); // 移动端侧边菜单显示状态
const currentMobileView = ref("list"); // 移动端当前主视图：'list', 'chat'
const screenWidth = ref(window.innerWidth); // 屏幕宽度

// 计算属性：是否为超小屏幕
const isVerySmallScreen = computed(() => {
  return screenWidth.value <= 480;
});

// 检测屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth;
  screenWidth.value = width; // 更新屏幕宽度
  isMobile.value = width <= 768;
  if (!isMobile.value) {
    showMobileMenu.value = false;
    currentMobileView.value = "list";
  }
};

// 移动端视图切换
const switchMobileView = (view) => {
  if (view === "menu") {
    showMobileMenu.value = !showMobileMenu.value; // 切换菜单显示状态
  } else {
    currentMobileView.value = view;
    showMobileMenu.value = false; // 切换到其他视图时关闭菜单
  }
};

// 移动端菜单遮罩点击
const closeMobileMenu = () => {
  showMobileMenu.value = false;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener("resize", checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkScreenSize);
});
// 聊天室配置
const localOptions = {
  // 添加好友模式，默认需要验证
  addFriendNeedVerify: true,
  // 群组加入模式，默认不需要验证
  //@ts-ignore
  teamJoinMode: V2NIMConst.V2NIMTeamJoinMode.V2NIM_TEAM_JOIN_MODE_FREE,
  // 群组被邀请模式，默认不需要验证
  teamAgreeMode:
    //@ts-ignore
    V2NIMConst.V2NIMTeamAgreeMode.V2NIM_TEAM_AGREE_MODE_NO_AUTH,
  // 单聊消息是否显示已读未读 默认 false
  p2pMsgReceiptVisible: true,
  // 群聊消息是否显示已读未读 默认 false
  teamMsgReceiptVisible: true,
  // 是否需要@消息 默认 true
  needMention: true,
  // 是否显示在线离线状态 默认 true
  loginStateVisible: true,
  // 是否允许转让群主
  allowTransferTeamOwner: true,
  // 是否需要显示群管理员相关主动功能，默认 false
  teamManagerVisible: true,
};
// 获取用户store
const userStore = useUserStore();
console.log(userStore.imInfo);
// 初始化IM系统
async function initIMSystem() {
  isInitializing.value = true;
  // 检查用户登录状态和IM信息
  if (!userStore.token) {
    throw new Error("用户未登录");
  }

  if (!userStore.imInfo?.account || !userStore.imInfo?.token) {
    throw new Error("缺少IM认证信息，请重新登录");
  }

  initOptions = {
    appkey: appkey,
    account: userStore.imInfo.account,
    token: userStore.imInfo.token,
  };
  const nim = V2NIM.getInstance({
    ...initOptions,
    // 是否开启云端会话，默认不开启
    enableV2CloudConversation: true,
    debugLevel: "debug",
    apiVersion: "v2",
  });
  // IM 连接
  nim.V2NIMLoginService.login(initOptions.account, initOptions.token, {
    retryCount: 5,
  });
  // 添加连接状态监听
  nim.V2NIMLoginService.on("onLoginStatus", (status) => {
    console.log("IM登录状态变化:", status);
  });
  // 初始化 UIKit 实例
  uikit = new IMUIKit({
    nim,
    singleton: true,
    localOptions,
  });
  uikitStore = uikit.getStateContext()?.store;
  // 加载聊天组件
  try {
    // 使用 Promise.all 等待所有 render 完成
    await Promise.all([
      uikit.render(
        SearchContainer,
        {
          onClickChat: () => {
            model.value = "chat";
          },
        },
        searchRef.value
      ),
      uikit.render(
        AddContainer,
        {
          onClickChat: () => {
            model.value = "chat";
          },
        },
        addRef.value
      ),
      uikit.render(MyAvatarContainer, null, avatarRef.value),
      uikit.render(
        ConversationContainer,
        {
          // 移动端点击会话时的处理逻辑
          onConversationItemClick: () => {
            if (isMobile.value) {
              // 超小屏幕（≤480px）完全切换到聊天视图
              if (isVerySmallScreen.value) {
                switchMobileView("chat");
              }
              // 中等屏幕（481-768px）保持分屏显示，但聚焦到聊天区域
              // 这里不需要特殊处理，保持默认的分屏布局
            }
          },
        },
        conversationRef.value
      ),
      uikit.render(
        ChatContainer,
        {
          // 以下是自定义渲染，用 compile 函数包裹 html 就可以了，注意 class 要写成 className
          // 安装并引入： import { compile } from "jsx-web-compiler";
          // renderHeader: () => compile(`<div className="my-header">123</div>`),
          // renderEmpty: () => compile("<div>欢迎使用家家指南</div>"),
          renderEmpty: () =>
            compile(
              `<div class="common-welcome-wrap"><img src="https://yx-web-nosdn.netease.im/common/630b48dc545af0633aaaa53bbd65cbb0/欢迎使用云信.png"></img>欢迎使用家家指南</div>`
            ),
        },
        chatRef.value
      ),
      uikit.render(ContactListContainer, null, contactListRef.value),
      uikit.render(
        ContactInfoContainer,
        {
          afterSendMsgClick: () => {
            model.value = "chat";
          },
          onGroupItemClick: () => {
            model.value = "chat";
          },
        },
        contactInfoRef.value
      ),
    ]);

    // 在这里执行需要等待所有 render 完成后的操作
    nextTick(() => {
      setTimeout(() => {
        isInitializing.value = false;
        console.log("uikit", uikitStore);
        checkUserChat();
      }, 3000);
    });
  } catch (error) {
    console.error("组件渲染失败:", error);
  }

  nextTick(() => {
    isInitializing.value = false;
  });
}

function renderCollection() {
  setTimeout(() => {
    uikit.render(ChatCollectionList, null, collectRef.value);
  }, 0);
}

function checkUserChat() {
  const myAccount = uikitStore.userStore.myUserInfo.accountId;
  console.log(myAccount);
  
  const conversationType = 1; // 单聊
  const receiverId = "84"; // 目标用户ID
  const conversationId = `${myAccount}|${conversationType}|${receiverId}`;
  console.log(uikitStore.conversationStore.conversations);
  
  // 如果会话已存在，直接选中
  if (uikitStore.conversationStore.conversations.get(conversationId)) {
    uikitStore.uiStore.selectConversation(conversationId);
  } else {
    // 如果还没有会话，插入并选中
    const isSelected = true;
    uikitStore.conversationStore.insertConversationActive(
      conversationType,
      receiverId,
      isSelected
    );
  }
}

onMounted(() => {
  initIMSystem();
});
</script>

<style scoped lang="scss">
/* 主容器样式 */
.chat-container {
  width: 100%;
  height: calc(100vh - 84px);
  background: #ffffff;
  position: relative;

  .container {
    width: 100%;
    height: 100%;

    .header {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #e8e8e8;
      position: relative;

      .mobile-menu-btn,
      .mobile-back-btn {
        position: absolute;
        left: 15px;
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #2a6bf2;
        font-size: 16px;

        i {
          margin-right: 5px;
        }
      }

      .search {
        width: 50%;
      }

      .add {
        margin-left: 20px;
      }
    }
    .content {
      width: 100%;
      height: calc(100% - 60px);
      display: flex;
    }

    .left {
      width: 60px;
      border-right: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      box-sizing: border-box;
      min-width: 60px;
      .avatar-icon {
        margin: 20px 0 25px 0;
        border-radius: 50%;
        border: 1px solid #e8e8e8;
      }

      .iconfont {
        font-size: 24px;
      }

      .chat-icon,
      .contact-icon {
        margin: 0 0 25px 0;
        font-size: 22px;
        color: rgba(0, 0, 0, 0.6);
        height: 45px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
      }

      .active {
        color: #2a6bf2;
      }

      .logout-icon {
        position: absolute;
        bottom: 10px;
        font-size: 22px;
        color: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        background-color: #000;
      }

      .icon-label {
        font-size: 12px;
        text-align: center;
      }
    }
    .right {
      flex: 1;
      display: flex;
      .right-list {
        width: 200px;
        border-right: 1px solid #e8e8e8;
      }

      .right-content {
        flex: 1;
      }
    }
    .collect {
      width: 100%;
      height: 100%;
      .collectRight {
        width: 100%;
        height: 100%;
      }
    }

    // 移动端布局样式
    .mobile-main {
      width: 100%;
      height: 100%;
      position: relative;
    }

    // 移动端聊天布局
    .mobile-chat-layout {
      width: 100%;
      height: 100%;
      display: flex;
      position: relative;
    }

    .mobile-conversation-panel {
      width: 35%;
      height: 100%;
      border-right: 1px solid #e8e8e8;
      transition: all 0.3s ease;
      overflow: hidden;

      &.collapsed {
        width: 0;
        border-right: none;
      }

      .right-list {
        width: 100%;
        height: 100%;
        border-right: none;
      }
    }

    .mobile-chat-panel {
      width: 65%;
      height: 100%;
      transition: all 0.3s ease;

      &.expanded {
        width: 100%;
      }

      .right-content {
        width: 100%;
        height: 100%;
      }
    }

    // 其他移动端页面
    .mobile-contact,
    .mobile-contact-info,
    .mobile-collect {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    // 移动端侧边菜单
    .mobile-menu-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999;
    }

    .mobile-menu {
      position: fixed;
      top: 0;
      left: -250px; // 初始隐藏在左侧
      width: 250px;
      height: 100%;
      background: #ffffff;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
      transition: left 0.3s ease;
      z-index: 1000;

      &.show {
        left: 0; // 显示时滑入
      }

      .mobile-menu-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 60px 20px 20px; // 顶部留出头部空间
        height: 100%;
        box-sizing: border-box;

        .avatar-icon {
          margin: 0 0 30px 0;
          border-radius: 50%;
          border: 1px solid #e8e8e8;
        }

        .chat-icon {
          margin: 0 0 30px 0;
          font-size: 22px;
          color: rgba(0, 0, 0, 0.6);
          height: 50px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
          padding: 10px;
          border-radius: 8px;
          transition: all 0.2s ease;

          &:hover {
            background: #f5f5f5;
          }

          &.active {
            color: #2a6bf2;
            background: #e6f3ff;
          }

          .icon-label {
            font-size: 12px;
            text-align: center;
            margin-top: 5px;
          }
        }

        .logout-icon {
          margin-top: auto; // 推到底部
          font-size: 22px;
          color: rgba(0, 0, 0, 0.6);
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          padding: 10px;
          border-radius: 8px;
          background-color: #f0f0f0;
        }
      }
    }
  }

  // 移动端特殊样式
  &.mobile-layout {
    .header {
      .search {
        width: calc(100% - 120px);
        margin: 0 10px;
      }

      .add {
        margin-left: 0;
      }
    }

    .content {
      height: calc(100% - 60px);
    }
  }
}

// 移动端媒体查询
@media (max-width: 768px) {
  .chat-container {
    height: calc(100vh - 50px); // 移动端减少顶部高度

    .container {
      .header {
        height: 50px; // 移动端头部高度
        padding: 0 10px;

        .mobile-menu-btn,
        .mobile-back-btn {
          left: 10px;
          font-size: 14px;
          z-index: 10;
        }

        .search {
          width: calc(100% - 100px);
          margin: 0 5px;
        }
      }

      .content {
        height: calc(100% - 50px);
      }

      // 移动端聊天布局调整
      .mobile-conversation-panel {
        width: 40%; // 稍微增加会话列表宽度

        &.collapsed {
          width: 0;
        }
      }

      .mobile-chat-panel {
        width: 60%;

        &.expanded {
          width: 100%;
        }
      }

      .mobile-menu {
        width: 220px; // 移动端菜单宽度稍小

        .mobile-menu-content {
          padding: 50px 15px 15px;

          .avatar-icon {
            margin: 0 0 25px 0;
          }

          .chat-icon {
            margin: 0 0 25px 0;
            font-size: 20px;
            height: 45px;

            .icon-label {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .chat-container {
    .container {
      .header {
        height: 45px;

        .mobile-menu-btn,
        .mobile-back-btn {
          font-size: 13px;
        }
      }

      .content {
        height: calc(100% - 45px);
      }

      // 超小屏幕下的聊天布局 - 完全切换模式
      .mobile-conversation-panel {
        width: 100%; // 显示时占满全屏

        &.collapsed {
          width: 0;
          display: none; // 完全隐藏
        }
      }

      .mobile-chat-panel {
        width: 0; // 默认隐藏

        &.expanded {
          width: 100%;
          display: block;
        }
      }

      .mobile-menu {
        width: 200px; // 超小屏幕菜单更窄

        .mobile-menu-content {
          padding: 45px 10px 10px;

          .avatar-icon {
            margin: 0 0 20px 0;
          }

          .chat-icon {
            font-size: 18px;
            height: 40px;
            margin: 0 0 20px 0;

            .icon-label {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
