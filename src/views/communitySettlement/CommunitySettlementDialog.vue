<template>
  <el-dialog
    title="新增社区"
    v-model="open"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="community-settlement-dialog"
    :fullscreen="isMobile"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-width="isMobile ? '70px' : '80px'"
      :label-position="isMobile ? 'top' : 'right'"
      class="community-form"
    >
      <el-form-item label="社区" prop="communityId">
        <el-select
          v-model="form.communityId"
          placeholder="请选择社区"
          style="width: 100%;"
          @change="handleCommunityChange"
        >
          <el-option
            v-for="community in communityList"
            :key="community.id"
            :label="community.name"
            :value="community.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入驻天数" prop="joinDays">
        <el-input
          v-model.number="form.joinDays"
          placeholder="请输入入驻天数"
          style="width: 100%;"
          @input="handleDaysInput"
          :formatter="(value) => value ? Math.floor(Math.abs(value)).toString() : ''"
          :parser="(value) => value ? Math.floor(Math.abs(parseFloat(value))) || 1 : 1"
        >
          <template #append>天</template>
        </el-input>
      </el-form-item>
      <el-form-item label="总费用" prop="totalFee">
        <el-input
          v-model="totalFeeDisplay"
          readonly
          style="width: 100%;"
        >
          <template #append>元</template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { listCommunity, joinCommunity } from '@/api/community';

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '500px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
  getCommunityList(); // 获取社区列表
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const open = ref(false);
const formRef = ref(null);
const communityList = ref([]);
const form = reactive({
  communityId: '',
  joinDays: 1,
});

// 计算总费用显示
const totalFeeDisplay = computed(() => {
  const selectedCommunity = communityList.value.find(c => c.id === form.communityId);
  if (selectedCommunity && form.joinDays > 0) {
    const totalFee = selectedCommunity.joinFee * form.joinDays;
    return totalFee.toFixed(2);
  }
  return '0.00';
});

const rules = reactive({
  communityId: [
    { required: true, message: "请选择社区", trigger: "change" }
  ],
  joinDays: [
    { required: true, message: "入驻天数不能为空", trigger: "blur" },
    { type: 'number', min: 1, message: "入驻天数必须大于0", trigger: "blur" }
  ]
});

const emit = defineEmits(['success']);

// onMounted已在上面处理，这里不需要重复

/** 获取社区列表 */
function getCommunityList() {
  listCommunity().then(response => {
    if (response.code === 200) {
      communityList.value = response.data || [];
    } else {
      ElMessage.error(response.message || '获取社区列表失败');
    }
  }).catch(error => {
    console.error('获取社区列表失败:', error);
    ElMessage.error('获取社区列表失败');
  });
}

/** 社区选择变化 */
function handleCommunityChange() {
  calculateTotalFee();
}

/** 处理天数输入 */
function handleDaysInput(value) {
  // 确保输入为正整数
  const numValue = Math.floor(Math.abs(parseFloat(value))) || 1;
  form.joinDays = numValue;
  calculateTotalFee();
}

/** 计算总费用 */
function calculateTotalFee() {
  // 总费用通过computed自动计算，这里可以添加其他逻辑
}

function openDialog() {
  open.value = true;
  reset();
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      const submitData = {
        communityId: form.communityId,
        joinDays: form.joinDays
      };

      joinCommunity(submitData).then(response => {
        if (response.code === 200) {
          ElMessage.success("入驻社区成功");
          open.value = false;
          emit('success');
        } else {
          ElMessage.error(response.message || '入驻失败');
        }
      }).catch(error => {
        console.error('入驻社区失败:', error);
        ElMessage.error('入驻失败');
      });
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.communityId = '';
  form.joinDays = 1;
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

// 暴露给父组件的方法
defineExpose({
  open: openDialog
});
</script>

<style lang="scss" scoped>
.community-settlement-dialog {
  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.community-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  text-align: right;
}

// 移动端适配
@media (max-width: 768px) {
  .community-settlement-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 15px;
      text-align: center;

      .el-button {
        width: 120px;
        margin: 0 8px;
      }
    }
  }

  .community-form {
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
      line-height: 32px;
      padding-right: 8px;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .community-settlement-dialog {
    :deep(.el-dialog__footer) {
      .el-button {
        width: 100px;
        margin: 0 5px;
        font-size: 12px;
      }
    }
  }

  .community-form {
    :deep(.el-form-item__label) {
      font-size: 12px;
      line-height: 30px;
    }
  }
}
</style>