<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="communitySettlementList">
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="社区" align="center" prop="communityName" />
      <el-table-column label="入驻天数 (天)" align="center" prop="joinDays" />
      <el-table-column label="剩余天数 (天)" align="center" prop="remainingDays" />
      <el-table-column label="总金额 (元)" align="center" prop="amount" />
      <el-table-column label="到期时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-if="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增社区对话框 -->
    <CommunitySettlementDialog ref="communitySettlementDialogRef" @success="getList" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue';
import CommunitySettlementDialog from './CommunitySettlementDialog.vue';
import { parseTime } from '@/utils/ruoyi';
import { ElMessage } from 'element-plus';
import { listMerchantInCommunity } from '@/api/community';

const loading = ref(false);
const total = ref(0);
const communitySettlementList = ref([]);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

const communitySettlementDialogRef = ref(null);

onMounted(() => {
  getList();
});

// keep-alive缓存页面时，重新进入页面获取新数据
onActivated(() => {
  getList();
});

/** 查询入驻社区列表 */
function getList() {
  loading.value = true;
  listMerchantInCommunity(queryParams).then(response => {
    if (response.code === 200) {
      communitySettlementList.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取数据失败');
    }
    loading.value = false;
  }).catch(error => {
    console.error('获取社区入驻列表失败:', error);
    ElMessage.error('获取数据失败');
    loading.value = false;
  });
}

/** 新增按钮操作 */
function handleAdd() {
  communitySettlementDialogRef.value.open();
}
</script>