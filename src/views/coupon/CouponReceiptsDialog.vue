<template>
  <el-dialog
    title="领取情况"
    :model-value="modelValue"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="coupon-receipts-dialog"
    :fullscreen="isMobile"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" :class="{ 'mobile-tabs': isMobile }">
      <el-tab-pane label="未使用" name="1"></el-tab-pane>
      <el-tab-pane label="已使用" name="2"></el-tab-pane>
      <el-tab-pane label="已失效" name="3"></el-tab-pane>
    </el-tabs>

    <el-table v-loading="loading" :data="receiptList" :class="{ 'mobile-table': isMobile }">
      <el-table-column label="用户头像" align="center" prop="avatar" :width="isMobile ? 60 : 100">
         <template #default="scope">
            <el-avatar :size="isMobile ? 30 : 40" :src="scope.row.avatar || defaultAvatar" />
         </template>
      </el-table-column>
      <el-table-column label="用户名" align="center" prop="username" :min-width="isMobile ? 80 : 120" />
      <el-table-column label="领取时间" align="center" prop="receivedAt" :width="isMobile ? 100 : 180">
        <template #default="scope">
          <span>{{ isMobile ? formatMobileTime(scope.row.receivedAt) : parseTime(scope.row.receivedAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="activeTab === '2'" label="核销时间" align="center" prop="usedTime" :width="isMobile ? 100 : 180">
        <template #default="scope">
          <span>{{ isMobile ? formatMobileTime(scope.row.usedTime) : parseTime(scope.row.usedTime) }}</span>
        </template>
      </el-table-column>
       <!-- 可以根据需要添加操作，例如 '关闭' 失效的券 -->
       <!--
       <el-table-column v-if="activeTab === 'expired'" label="操作" align="center" width="100">
         <template #default="scope">
           <el-button link type="primary" @click="handleCloseExpired(scope.row)">关闭</el-button>
         </template>
       </el-table-column>
       -->
    </el-table>

    <!-- 分页 -->

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, computed, onMounted, onUnmounted } from 'vue';
import { getCouponReceiveStatus } from '@/api/system/coupon';
import defaultAvatar from '@/assets/images/profile.jpg'; // 恢复为原始的默认头像

const props = defineProps({
  modelValue: Boolean, // 控制弹窗显示/隐藏
  couponId: { // 当前查看的优惠券ID
    type: [Number, String],
    default: null
  }
});

const emit = defineEmits(['update:modelValue']);

const { proxy } = getCurrentInstance();
const { parseTime } = proxy; // 假设 parseTime 在全局挂载

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '800px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// 移动端时间格式化
const formatMobileTime = (time) => {
  if (!time) return '';
  const date = new Date(time);
  return `${date.getMonth() + 1}/${date.getDate()}`;
};

const loading = ref(false);
const total = ref(0);
const receiptList = ref([]);
const activeTab = ref('1'); // 默认显示未使用

const queryParams = reactive({
  status: 1, // 默认查询未使用状态
  couponsId: null
});

// 监听 couponId 变化，当弹窗打开且 couponId 有效时，获取数据
watch(() => [props.modelValue, props.couponId], ([newModelValue, newCouponId]) => {
  if (newModelValue && newCouponId) {
    queryParams.couponsId = newCouponId;
    // 重置标签页，然后获取数据
    activeTab.value = '1';
    queryParams.status = 1;
    getReceipts();
  } else {
    // 关闭弹窗时清空列表
    receiptList.value = [];
    total.value = 0;
  }
}, { immediate: true });

// 获取领取记录列表
function getReceipts() {
  if (!queryParams.couponsId) return; // 防止无效请求
  loading.value = true;
  getCouponReceiveStatus(queryParams.couponsId, queryParams.status).then(response => {
    receiptList.value = response.data || [];
    total.value = response.data ? response.data.length : 0;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// Tab 切换事件
function handleTabClick(tab) {
  queryParams.status = parseInt(tab.paneName, 10);
  getReceipts();
}

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false);
}

// 处理关闭失效券的操作 (如果需要)
// function handleCloseExpired(row) {
//   proxy.$modal.confirm(`确认要关闭用户"${row.nickname}"的这张已失效优惠券吗？`).then(() => {
//     // TODO: 调用关闭接口
//     return Promise.resolve(); // 模拟API调用
//   }).then(() => {
//     getReceipts(); // 重新加载列表
//     proxy.$modal.msgSuccess("操作成功");
//   }).catch(() => {});
// }

</script>

<style lang="scss" scoped>
.coupon-receipts-dialog {
  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
      border-top: 1px solid #f0f0f0;
    }
  }
}

/* 调整分页组件的上边距 */
.el-pagination {
  margin-top: 15px;
}

// 移动端适配
@media (max-width: 768px) {
  .coupon-receipts-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 15px;
      text-align: center;

      .el-button {
        width: 120px;
      }
    }
  }

  // 移动端标签页样式调整
  .mobile-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__item) {
      padding: 0 15px;
      font-size: 13px;
      height: 36px;
      line-height: 36px;
    }

    :deep(.el-tabs__nav-wrap) {
      padding: 0 5px;
    }
  }

  // 移动端表格样式调整
  .mobile-table {
    :deep(.el-table__header) {
      th {
        padding: 8px 4px;
        font-size: 12px;
      }
    }

    :deep(.el-table__body) {
      td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }

    :deep(.el-button--small) {
      padding: 4px 8px;
      font-size: 11px;
    }
  }

  // 移动端分页样式调整
  .el-pagination {
    justify-content: center;
    margin-top: 15px;

    :deep(.el-pagination__total),
    :deep(.el-pagination__jump) {
      display: none;
    }

    :deep(.el-pagination__sizes) {
      display: none;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .mobile-tabs {
    :deep(.el-tabs__item) {
      padding: 0 10px;
      font-size: 12px;
      height: 32px;
      line-height: 32px;
    }
  }

  .mobile-table {
    :deep(.el-table__header) {
      th {
        padding: 6px 2px;
        font-size: 11px;
      }
    }

    :deep(.el-table__body) {
      td {
        padding: 6px 2px;
        font-size: 11px;
      }
    }

    :deep(.el-avatar) {
      width: 25px !important;
      height: 25px !important;
    }

    :deep(.el-button--small) {
      padding: 2px 6px;
      font-size: 10px;
    }
  }

  .coupon-receipts-dialog {
    :deep(.el-dialog__footer) {
      .el-button {
        width: 100px;
        font-size: 12px;
      }
    }
  }
}
</style>