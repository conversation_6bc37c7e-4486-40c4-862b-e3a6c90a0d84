<template>
   <div class="app-container">
      <!-- 操作区域 -->
      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['marketing:coupon:add']"
            >新增优惠券</el-button>
         </el-col>
      </el-row>

      <!-- Tabs 切换 -->
      <el-tabs v-model="queryParams.type" class="mb8">
         <el-tab-pane label="满减券" :name="1"></el-tab-pane>
         <el-tab-pane label="折扣券" :name="2"></el-tab-pane>
      </el-tabs>

      <!-- 优惠券列表 -->
      <el-table v-loading="loading" :data="couponList">
         <el-table-column v-if="queryParams.type === 1" label="满减金额" align="center" min-width="160">
            <template #default="scope">
               <span>满{{ scope.row.minOrderAmount }}减{{ scope.row.discountValue }}</span>
            </template>
         </el-table-column>
         <el-table-column v-if="queryParams.type === 2" label="折扣额" align="center" min-width="100">
            <template #default="scope">
               <span>{{ scope.row.discountValue }}折</span>
            </template>
         </el-table-column>
         <el-table-column label="数量" align="center" prop="totalQuantity" min-width="80" />
         <el-table-column label="剩余数量" align="center" min-width="100">
            <template #default="scope">
               <span>{{ (scope.row.totalQuantity || 0) - (scope.row.receiveNum || 0) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="status" min-width="100">
            <template #default="scope">
               <el-tag v-if="scope.row.status === 1" type="success">上架</el-tag>
               <el-tag v-else-if="scope.row.status === 2" type="warning">下架</el-tag>
               <el-tag v-else type="info">未知</el-tag>
            </template>
         </el-table-column>
         <el-table-column label="剩余有效期" align="center" prop="remainTime" min-width="120">
             <template #default="scope">
                <span>{{ formatExpiresIn(scope.row.remainTime * 1000) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createdAt" min-width="160">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260">
           <template #default="scope">
             <el-button link type="primary" @click="handleReceipts(scope.row)" v-hasPermi="['marketing:coupon:receipts']">领取情况</el-button>
             <!-- 根据状态显示上架或下架按钮 -->
             <el-button
               v-if="scope.row.status === 1"
               link
               type="warning"
               @click="handleTakeDown(scope.row)"
               v-hasPermi="['marketing:coupon:takedown']"
             >
               下架
             </el-button>
             <el-button
               v-if="scope.row.status === 2"
               link
               type="success"
               @click="handleTakeUp(scope.row)"
               v-hasPermi="['marketing:coupon:takeup']"
             >
               上架
             </el-button>
             <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['marketing:coupon:remove']">删除</el-button>
           </template>
         </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 新增/编辑优惠券表单弹窗 -->
      <coupon-form
         v-model="dialogVisible"
         :coupon-data="currentCoupon"
         :is-edit="isEditMode"
         @submitSuccess="getList"
      />

      <!-- 领取情况弹窗 -->
      <coupon-receipts-dialog
         v-model="receiptsDialogVisible"
         :coupon-id="currentCouponId"
      />

   </div>
</template>

<script setup name="Coupon">
import { ref, reactive, onMounted, getCurrentInstance, watch, onActivated } from 'vue';
import CouponForm from './CouponForm.vue'; // 引入表单组件
import CouponReceiptsDialog from './CouponReceiptsDialog.vue'; // 引入领取情况弹窗组件
import { listCoupon, delCoupon, updateCouponStatus } from "@/api/system/coupon";

const { proxy } = getCurrentInstance();
const { parseTime } = proxy;

// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 优惠券表格数据
const couponList = ref([]);
// 弹窗相关状态
const dialogVisible = ref(false); // 控制新增/编辑弹窗显示
const receiptsDialogVisible = ref(false); // 控制领取情况弹窗显示
const isEditMode = ref(false); // 是否为编辑模式
const currentCoupon = ref({}); // 当前操作（编辑）的优惠券数据
const currentCouponId = ref(null); // 当前查看领取情况的优惠券ID

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: 1, // 默认显示满减券
});

/** 查询优惠券列表 */
function getList() {
  if(loading.value) return
  couponList.value = []
  loading.value = true;
  listCoupon(queryParams).then(response => {
    couponList.value = response.data.list || [];
    total.value = response.data.total || 0;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
    proxy.$modal.msgError("获取优惠券列表失败");
  });
}

/** 新增按钮操作 */
function handleAdd() {
  isEditMode.value = false;
  currentCoupon.value = { type: queryParams.type }; // 传递当前选中的类型
  dialogVisible.value = true; // 打开新增/编辑弹窗
}

/** 编辑按钮操作 (如果需要从列表直接编辑) */
// function handleUpdate(row) {
//   const couponId = row.couponId;
//   // 实际应调用API: getCoupon(couponId).then(response => {
//   //   currentCoupon.value = response.data;
//   //   isEditMode.value = true;
//   //   dialogVisible.value = true;
//   // });
//   // 模拟获取数据
//   currentCoupon.value = { ...row }; // 填充当前优惠券数据
//   isEditMode.value = true;
//   dialogVisible.value = true; // 打开新增/编辑弹窗
// }

/** 下架按钮操作 */
function handleTakeDown(row) {
  const couponDesc = `${row.type === 1 ? '满减券' : '折扣券'}`;
  proxy.$modal.confirm(`确认要下架这个${couponDesc}吗？`).then(() => {
    return updateCouponStatus({ couponsId: row.id, status: 2 }); // 2-下架
  }).then(() => {
    getList(); // 重新加载列表
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => {});
}

/** 上架按钮操作 */
function handleTakeUp(row) {
  const couponDesc = `${row.type === 1 ? '满减券' : '折扣券'}`;
  proxy.$modal.confirm(`确认要上架这个${couponDesc}吗？`).then(() => {
    return updateCouponStatus({ couponsId: row.id, status: 1 }); // 1-上架
  }).then(() => {
    getList(); // 重新加载列表
    proxy.$modal.msgSuccess("上架成功");
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const couponId = row.id;
  const couponDesc = `${row.type === 1 ? '满减券' : '折扣券'}`;
  proxy.$modal.confirm(`是否确认删除这个${couponDesc}？`).then(function() {
    return delCoupon(couponId);
  }).then(() => {
    getList(); // 重新加载列表
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 领取情况按钮操作 */
function handleReceipts(row) {
    currentCouponId.value = row.id;
    receiptsDialogVisible.value = true;
}

/** 格式化剩余有效期 */
function formatExpiresIn(milliseconds) {
    if (milliseconds <= 0) return '已过期或永久';
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    const h = hours % 24;
    const m = minutes % 60;
    const s = seconds % 60;

    let result = '';
    if (days > 0) result += `${days}天`;
    if (h > 0) result += `${h}时`;
    if (m > 0) result += `${m}分`;
    if (s > 0 && days === 0 && h === 0) result += `${s}秒`; // 仅在不足1分钟时显示秒

    return result || '即将过期';
}

// 监听类型切换
watch(() => queryParams.type, () => {
  queryParams.pageNum = 1; // 重置页码
  getList(); // 重新获取数据
});

onMounted(() => {
  getList(); // 组件挂载后获取列表数据
});

// keep-alive 组件激活时重新获取数据
onActivated(() => {
  getList(); // 每次进入页面都获取最新数据
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 10px;
}
.el-tabs {
  margin-bottom: 15px;
}
/* 可以根据需要调整表格高度 */
.el-table {
  height: calc(100vh - 340px) !important;
}
</style>