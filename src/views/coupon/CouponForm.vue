<template>
  <el-dialog
    :title="isEdit ? '编辑优惠券' : '新增优惠券'"
    :model-value="modelValue"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="coupon-dialog"
    :fullscreen="isMobile"
    :before-close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      label-width="120px"
      class="coupon-form"
    >
      <!-- 第一行：类型、数量、有效期 -->
      <el-row :gutter="isMobile ? 0 : 24">
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="类型" prop="type">
            <el-select
              v-model="formData.type"
              placeholder="请选择类型"
              :disabled="isEdit"
              style="width: 100%"
            >
              <el-option label="满减" :value="1" />
              <el-option label="折扣" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="数量" prop="totalQuantity">
            <el-input
              v-model="formData.totalQuantity"
              placeholder="30"
              @input="handleQuantityInput"
            >
              <template #append>张</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="有效期" prop="effectiveDays">
            <el-input
              v-model="formData.effectiveDays"
              placeholder="30"
              @input="handleDaysInput"
            >
              <template #append>天</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：满减金额或折扣额 -->
      <el-row :gutter="isMobile ? 0 : 24">
        <!-- 满减券配置 -->
        <el-col :span="isMobile ? 24 : 8" v-if="formData.type === 1">
          <el-form-item label="满减金额" prop="fullReductionAmount">
            <div class="full-reduction-input">
              <span class="prefix-text">满</span>
              <el-input
                v-model="formData.minOrderAmount"
                placeholder="0.00"
                @input="handleMinAmountInput"
                :formatter="
                  (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                "
                :parser="(value) => value.replace(/,/g, '')"
              />
              <span class="middle-text">减</span>
              <el-input
                v-model="formData.discountValue"
                placeholder="0.00"
                @input="handleDiscountAmountInput"
                :formatter="
                  (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                "
                :parser="(value) => value.replace(/,/g, '')"
              />
            </div>
          </el-form-item>
        </el-col>

        <!-- 折扣券配置 -->
        <el-col :span="isMobile ? 24 : 8" v-if="formData.type === 2">
          <el-form-item label="折扣额" prop="discountValue">
            <div class="discount-input">
              <el-input
                v-model="formData.discountValue"
                placeholder="1-9"
                @input="handleDiscountInput"
              >
                <template #append>折</template>
              </el-input>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="总费用">
            <el-input
              :value="totalCost"
              readonly
              placeholder="0"
            >
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="handleSubmit" class="submit-btn"
          >确认发布</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, computed, onMounted, onUnmounted } from "vue";
import { createCoupon, updateCoupon } from "@/api/system/coupon";

const props = defineProps({
  modelValue: Boolean, // 控制弹窗显示/隐藏
  couponData: {
    // 编辑时传入的优惠券数据
    type: Object,
    default: () => ({}),
  },
  isEdit: {
    // 是否为编辑模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:modelValue", "submitSuccess"]);

const { proxy } = getCurrentInstance();
const formRef = ref(null);

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '800px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const initialFormData = {
  id: undefined,
  type: 1, // 默认满减券
  totalQuantity: "",
  effectiveDays: "30",
  minOrderAmount: "", // 满减券：满多少
  discountValue: "", // 满减券：减多少 / 折扣券：多少折
};

const formData = reactive({ ...initialFormData });

// 计算总费用：数量 * 0.1
const totalCost = computed(() => {
  const quantity = parseInt(formData.totalQuantity) || 0;
  return (quantity * 0.1).toFixed(2);
});

// 校验规则
const rules = reactive({
  type: [{ required: true, message: "请选择优惠券类型", trigger: "change" }],
  totalQuantity: [
    { required: true, message: "请输入数量", trigger: "blur" },
    {
      validator: (_, value, callback) => {
        if (!value || value === "") {
          callback(new Error("请输入数量"));
        } else if (!/^\d+$/.test(value.toString()) || parseInt(value) < 1) {
          callback(new Error("数量必须为正整数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  effectiveDays: [
    { required: true, message: "请输入有效期", trigger: "blur" },
    {
      validator: (_, value, callback) => {
        if (!value || value === "") {
          callback(new Error("请输入有效期"));
        } else if (!/^\d+$/.test(value.toString()) || parseInt(value) < 1) {
          callback(new Error("有效期必须为正整数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  fullReductionAmount: [
    {
      validator: (_, _value, callback) => {
        if (formData.type === 1) {
          if (
            !formData.minOrderAmount ||
            formData.minOrderAmount === "" ||
            !formData.discountValue ||
            formData.discountValue === ""
          ) {
            callback(new Error("请输入完整的满减金额"));
          } else if (
            !/^\d+(\.\d{1,2})?$/.test(formData.minOrderAmount.toString()) ||
            parseFloat(formData.minOrderAmount) <= 0
          ) {
            callback(new Error("满金额必须为正数，最多两位小数"));
          } else if (
            !/^\d+(\.\d{1,2})?$/.test(formData.discountValue.toString()) ||
            parseFloat(formData.discountValue) <= 0
          ) {
            callback(new Error("减金额必须为正数，最多两位小数"));
          } else if (
            parseFloat(formData.discountValue) >=
            parseFloat(formData.minOrderAmount)
          ) {
            callback(new Error("减金额必须小于满金额"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  discountValue: [
    {
      validator: (_, value, callback) => {
        if (formData.type === 2) {
          if (!value || value === "") {
            callback(new Error("请输入折扣"));
          } else if (
            !/^\d+$/.test(value.toString()) ||
            parseInt(value) < 1 ||
            parseInt(value) > 99
          ) {
            callback(new Error("折扣必须为1-99的整数"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});

// 监听外部传入的数据，用于编辑时填充表单
watch(
  () => props.couponData,
  (newData) => {
    if (props.isEdit && newData && newData.id) {
      Object.assign(formData, newData);
    }
  },
  { immediate: true, deep: true }
);

// 监听弹窗打开，重置表单（如果是新增模式）
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      if (!props.isEdit) {
        // 如果是新增，根据传入的 couponData.type 设置默认类型
        const defaultType = props.couponData?.type || 1;
        resetForm({ ...initialFormData, type: defaultType });
      } else {
        // 编辑模式下，确保数据已填充
        if (props.couponData && props.couponData.id) {
          Object.assign(formData, props.couponData);
        }
      }
    }
  }
);

// 处理数量输入（只允许正整数）
function handleQuantityInput(value) {
  const numericValue = value.replace(/[^\d]/g, "");
  if (numericValue === "" || numericValue === "0") {
    formData.totalQuantity = "";
  } else {
    formData.totalQuantity = numericValue;
  }
}

// 处理有效天数输入（只允许正整数）
function handleDaysInput(value) {
  const numericValue = value.replace(/[^\d]/g, "");
  if (numericValue === "" || numericValue === "0") {
    formData.effectiveDays = "";
  } else {
    formData.effectiveDays = numericValue;
  }
}

// 处理满减金额输入（只允许正数，最多两位小数）
function handleMinAmountInput(value) {
  let numericValue = value.replace(/[^\d.]/g, "");
  const parts = numericValue.split(".");
  if (parts.length > 2) {
    numericValue = parts[0] + "." + parts.slice(1).join("");
  }
  if (parts.length === 2 && parts[1].length > 2) {
    numericValue = parts[0] + "." + parts[1].substring(0, 2);
  }
  if (numericValue.startsWith(".")) {
    numericValue = "0" + numericValue;
  }
  formData.minOrderAmount = numericValue;
}

// 处理优惠金额输入（只允许正数，最多两位小数）
function handleDiscountAmountInput(value) {
  let numericValue = value.replace(/[^\d.]/g, "");
  const parts = numericValue.split(".");
  if (parts.length > 2) {
    numericValue = parts[0] + "." + parts.slice(1).join("");
  }
  if (parts.length === 2 && parts[1].length > 2) {
    numericValue = parts[0] + "." + parts[1].substring(0, 2);
  }
  if (numericValue.startsWith(".")) {
    numericValue = "0" + numericValue;
  }
  formData.discountValue = numericValue;
}

// 处理折扣输入（只允许1-99的整数）
function handleDiscountInput(value) {
  const numericValue = value.replace(/[^\d]/g, "");
  if (numericValue === "" || numericValue === "0") {
    formData.discountValue = "";
  } else if (parseInt(numericValue) > 99) {
    formData.discountValue = "99";
  } else {
    formData.discountValue = numericValue;
  }
}



// 重置表单方法
function resetForm(data = initialFormData) {
  Object.assign(formData, data);
  if (formRef.value) {
    formRef.value.resetFields();
    Object.assign(formData, initialFormData, data);
  }
}

// 提交表单
function handleSubmit() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      const submitData = { ...formData };

      // 数据类型转换，确保符合API要求
      if (submitData.totalQuantity) {
        submitData.totalQuantity = parseInt(submitData.totalQuantity, 10);
      }

      if (submitData.effectiveDays) {
        submitData.effectiveDays = parseInt(submitData.effectiveDays, 10);
      }

      if (submitData.type === 1) {
        // 满减券
        if (submitData.minOrderAmount) {
          submitData.minOrderAmount = parseFloat(submitData.minOrderAmount);
        }
        if (submitData.discountValue) {
          submitData.discountValue = parseFloat(submitData.discountValue);
        }
      } else if (submitData.type === 2) {
        // 折扣券
        if (submitData.discountValue) {
          submitData.discountValue = parseInt(submitData.discountValue, 10);
        }
        // 清除满减券相关字段
        submitData.minOrderAmount = undefined;
      }

      console.log("提交的数据:", submitData);

      proxy.$modal.loading("正在提交...");
      const apiCall = props.isEdit
        ? updateCoupon(submitData)
        : createCoupon(submitData);

      apiCall
        .then(() => {
          proxy.$modal.closeLoading();
          proxy.$modal.msgSuccess(props.isEdit ? "修改成功" : "新增成功");
          emit("update:modelValue", false);
          emit("submitSuccess");
        })
        .catch(() => {
          proxy.$modal.closeLoading();
          proxy.$modal.msgError("操作失败");
        });
    } else {
      console.log("表单校验失败");
      return false;
    }
  });
}

// 取消按钮
function handleCancel() {
  emit("update:modelValue", false);
}
</script>

<style lang="scss" scoped>
.coupon-dialog {
  :deep(.el-dialog__header) {
    padding: 20px 24px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    font-size: 16px;
    color: #333;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }

  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
    }
  }
}

.coupon-form {
  :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  :deep(.el-form-item__label) {
    font-weight: 400;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    padding-right: 12px;
  }

  // 满减金额输入框样式
  .full-reduction-input {
    display: flex;
    align-items: center;
    gap: 8px;

    .prefix-text,
    .middle-text,
    .suffix-text {
      font-size: 14px;
      color: #666;
      white-space: nowrap;
    }

    .el-input {
      flex: 1;
    }
  }

  // 折扣输入框样式
  .discount-input {
    display: flex;
    align-items: center;
    gap: 8px;

    .suffix-text {
      font-size: 14px;
      color: #666;
      white-space: nowrap;
    }

    .el-input {
      flex: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-btn {
    padding: 8px 24px;
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    color: #666;
    font-size: 14px;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  .submit-btn {
    padding: 8px 24px;
    height: 36px;
    border-radius: 6px;
    background: #1890ff;
    border-color: #1890ff;
    font-size: 14px;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .coupon-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 15px;
    }
  }

  .coupon-form {
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
      line-height: 36px;
      padding-right: 8px;
    }

    // 移动端满减金额输入框样式调整
    .full-reduction-input {
      gap: 6px;

      .prefix-text,
      .middle-text,
      .suffix-text {
        font-size: 13px;
      }
    }

    // 移动端折扣输入框样式调整
    .discount-input {
      gap: 6px;

      .suffix-text {
        font-size: 13px;
      }
    }
  }

  .dialog-footer {
    justify-content: center;
    gap: 15px;

    .cancel-btn,
    .submit-btn {
      width: 120px;
      padding: 8px 16px;
      font-size: 13px;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .coupon-form {
    // 超小屏幕下满减输入框垂直布局
    .full-reduction-input {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;

      .prefix-text,
      .middle-text {
        text-align: center;
        padding: 4px 0;
        font-size: 12px;
      }
    }
  }

  .dialog-footer {
    .cancel-btn,
    .submit-btn {
      width: 100px;
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}
</style>
