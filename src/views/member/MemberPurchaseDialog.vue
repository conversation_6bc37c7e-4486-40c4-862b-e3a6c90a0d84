<template>
  <el-dialog
    title="购买费用"
    v-model="dialogVisible"
    :width="dialogWidth"
    :top="dialogTop"
    :close-on-click-modal="false"
    @close="handleClose"
    class="member-purchase-dialog"
    :fullscreen="isMobile"
  >
    <div class="purchase-content">
      <el-alert
        title="费用 = 数量 x 系统设置的单价"
        type="info"
        :closable="false"
        show-icon
      />

      <!-- 数量选择 -->
      <div class="quantity-section">
        <label class="quantity-label">购买数量：</label>
        <el-input-number
          v-model="memberQuantity"
          :min="1"
          :max="999"
          controls-position="right"
          :style="{ width: isMobile ? '120px' : '150px' }"
        />
      </div>

      <!-- 价格计算显示 -->
      <div class="price-calculation">
        <div class="calculation-row">
          <span>单价：</span>
          <span class="unit-price">{{ unitPrice.toFixed(2) }} 元</span>
        </div>
        <div class="calculation-row">
          <span>数量：</span>
          <span>{{ memberQuantity }}</span>
        </div>
        <div class="calculation-row total-row">
          <span>总价：</span>
          <span class="total-price">{{ totalPrice.toFixed(2) }} 元</span>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认购买</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, getCurrentInstance, onMounted, onUnmounted } from 'vue';
import { getSystemConfig, buyMember } from '@/api/member';

const { proxy } = getCurrentInstance();

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '30%');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'submitSuccess']);

const dialogVisible = ref(props.modelValue);
const memberQuantity = ref(1); // 购买数量
const unitPrice = ref(0); // 系统设置的单价
const submitLoading = ref(false); // 提交按钮加载状态

const totalPrice = computed(() => {
  return memberQuantity.value * unitPrice.value;
});

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
  if (val) {
    // 弹窗打开时获取系统配置
    fetchSystemConfig();
  }
});

// 获取系统配置
async function fetchSystemConfig() {
  try {
    const response = await getSystemConfig();
    if (response.code === 200) {
      unitPrice.value = response.data.memberCharge || 0;
    } else {
      proxy.$modal.msgError('获取系统配置失败');
    }
  } catch (error) {
    console.error('获取系统配置失败:', error);
    proxy.$modal.msgError('获取系统配置失败');
  }
}

function handleClose() {
  emit('update:modelValue', false);
  // 重置数据
  memberQuantity.value = 1;
  unitPrice.value = 0;
}

function handleCancel() {
  dialogVisible.value = false;
  handleClose();
}

async function handleSubmit() {
  if (memberQuantity.value <= 0) {
    proxy.$modal.msgError('请选择有效的购买数量');
    return;
  }

  submitLoading.value = true;

  try {
    const response = await buyMember(memberQuantity.value);
    if (response.code === 200) {
      proxy.$modal.msgSuccess('购买成功');
      emit('submitSuccess');
      dialogVisible.value = false;
      handleClose();
    } else {
      proxy.$modal.msgError(response.message || '购买失败');
    }
  } catch (error) {
    console.error('购买失败:', error);
    proxy.$modal.msgError('购买失败，请稍后重试');
  } finally {
    submitLoading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.member-purchase-dialog {
  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.purchase-content {
  text-align: center;
  padding: 20px 0;

  .el-alert {
    margin-bottom: 20px;
    justify-content: center;
  }

  .quantity-section {
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    .quantity-label {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .price-calculation {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .calculation-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;

      &:last-child {
        margin-bottom: 0;
      }

      &.total-row {
        border-top: 1px solid #e4e7ed;
        padding-top: 8px;
        margin-top: 8px;
        font-weight: bold;
        font-size: 16px;
        color: #303133;

        .total-price {
          color: #f56c6c;
          font-size: 18px;
        }
      }
    }

    .unit-price {
      color: #409eff;
      font-weight: 500;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .member-purchase-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 15px;
      text-align: center;

      .el-button {
        width: 120px;
        margin: 0 8px;
      }
    }
  }

  .purchase-content {
    padding: 15px 0;

    .el-alert {
      margin-bottom: 15px;
      :deep(.el-alert__title) {
        font-size: 13px;
      }
    }

    .quantity-section {
      margin: 15px 0;
      gap: 8px;

      .quantity-label {
        font-size: 13px;
      }
    }

    .price-calculation {
      margin-top: 15px;
      padding: 12px;

      .calculation-row {
        font-size: 13px;
        margin-bottom: 6px;

        &.total-row {
          padding-top: 6px;
          margin-top: 6px;
          font-size: 15px;

          .total-price {
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .member-purchase-dialog {
    :deep(.el-dialog__footer) {
      .el-button {
        width: 100px;
        margin: 0 5px;
        font-size: 12px;
      }
    }
  }

  .purchase-content {
    .el-alert {
      :deep(.el-alert__title) {
        font-size: 12px;
      }
    }

    .quantity-section {
      flex-direction: column;
      gap: 10px;

      .quantity-label {
        font-size: 12px;
      }
    }

    .price-calculation {
      padding: 10px;

      .calculation-row {
        font-size: 12px;

        &.total-row {
          font-size: 14px;

          .total-price {
            font-size: 15px;
          }
        }
      }
    }
  }
}
</style>