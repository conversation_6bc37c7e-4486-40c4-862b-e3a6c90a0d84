<template>
  <el-dialog
    title="会员详情"
    v-model="dialogVisible"
    :width="dialogWidth"
    :top="dialogTop"
    :close-on-click-modal="false"
    @close="handleClose"
    class="member-detail-dialog"
    :fullscreen="isMobile"
  >
    <div class="detail-content" v-loading="loading">
      <div class="top-section">
        <el-avatar :size="isMobile ? 40 : 50" :src="memberData.avatar"></el-avatar>
        <div class="info">
          <div class="name">昵称：{{ memberData.username }}</div>
          <div class="community">所属社区：{{ memberData.communityName }}</div>
        </div>
      </div>
      <el-divider></el-divider>
      <el-row :gutter="isMobile ? 0 : 20" class="data-row">
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">成为会员时间</span>
            <span class="value">{{ parseTime(memberData.createdAt) }}</span>
          </div>
        </el-col>
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">平台优惠券数量</span>
            <span class="value">{{ memberDetail.platformCouponNum || 0 }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="isMobile ? 0 : 20" class="data-row">
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">商家优惠券数量</span>
            <span class="value">{{ memberDetail.merchantCouponNum || 0 }}</span>
          </div>
        </el-col>
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">平台优惠券消费数量</span>
            <span class="value">{{ memberDetail.platformCouponConsumeNum || 0 }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="isMobile ? 0 : 20" class="data-row">
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">商家优惠券消费数量</span>
            <span class="value">{{ memberDetail.couponNum || 0 }}</span>
          </div>
        </el-col>
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">关联商家会员数量</span>
            <span class="value">{{ memberDetail.memberNum || 0 }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="isMobile ? 0 : 20" class="data-row">
        <el-col :span="isMobile ? 24 : 12">
          <div class="data-item">
            <span class="label">消费范围距离社区</span>
            <span class="value">{{ memberDetail.distance ? memberDetail.distance + 'km' : '0km' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance, computed, onMounted, onUnmounted } from 'vue';
import { queryMemberById } from '@/api/member';

const { proxy } = getCurrentInstance();
const { parseTime } = proxy;

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '30%');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  memberData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

const dialogVisible = ref(props.modelValue);
const loading = ref(false);
const memberDetail = ref({});

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
  if (val && props.memberData.id) {
    getMemberDetail();
  }
});

// 获取会员详情信息
async function getMemberDetail() {
  if (!props.memberData.id) return;

  loading.value = true;
  try {
    const response = await queryMemberById(props.memberData.id);
    if (response.code === 200 && response.data) {
      memberDetail.value = response.data;
    } else {
      proxy.$modal.msgError(response.message || '获取会员详情失败');
    }
  } catch (error) {
    console.error('获取会员详情失败:', error);
    proxy.$modal.msgError('获取会员详情失败');
  } finally {
    loading.value = false;
  }
}

function handleClose() {
  emit('update:modelValue', false);
  memberDetail.value = {}; // 清空详情数据
}
</script>

<style lang="scss" scoped>
.member-detail-dialog {
  .el-dialog__header {
    padding: 0;
  }
  .my-header {
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 0 0;
    .el-icon {
      font-size: 20px;
      cursor: pointer;
    }
  }
  .detail-content {
    padding: 0 20px 20px;
  }

  .top-section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .el-avatar {
      margin-right: 15px;
    }

    .info {
      .name {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .community {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .el-divider {
    margin: 10px 0 20px 0;
  }

  .data-row {
    margin-bottom: 15px;
  }

  .data-item {
    display: flex;
    flex-direction: column;

    .label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 5px;
    }

    .value {
      font-size: 15px;
      color: #303133;
      font-weight: bold;
    }
  }

  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .member-detail-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }

    .detail-content {
      padding: 0 10px 10px;
    }

    .top-section {
      margin-bottom: 15px;

      .el-avatar {
        margin-right: 12px;
      }

      .info {
        .name {
          font-size: 15px;
          margin-bottom: 4px;
        }
        .community {
          font-size: 13px;
        }
      }
    }

    .el-divider {
      margin: 8px 0 15px 0;
    }

    .data-row {
      margin-bottom: 12px;
    }

    .data-item {
      margin-bottom: 12px;

      .label {
        font-size: 13px;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
      }
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .member-detail-dialog {
    .top-section {
      .el-avatar {
        margin-right: 10px;
      }

      .info {
        .name {
          font-size: 14px;
        }
        .community {
          font-size: 12px;
        }
      }
    }

    .data-item {
      .label {
        font-size: 12px;
      }

      .value {
        font-size: 13px;
      }
    }
  }
}
</style>