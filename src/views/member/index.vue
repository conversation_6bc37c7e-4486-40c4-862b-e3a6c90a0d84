<template>
   <div class="app-container">
      <!-- 操作区域 -->
      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <div class="member-info-section">
               <span class="member-count-info">
                  剩余可用会员数：<span class="count-number">{{ availableMemberCount }}</span>
               </span>
               <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handlePurchase"
                  v-hasPermi="['member:purchase:add']"
                  style="margin-left: 20px"
               >购买会员</el-button>
            </div>
         </el-col>
      </el-row>

      <!-- 会员列表 -->
      <el-table v-loading="loading" :data="memberList">
        <el-table-column label="用户头像" align="center" prop="avatar" width="80">
            <template #default="scope">
                <el-avatar :src="scope.row.avatar"></el-avatar>
            </template>
        </el-table-column>
        <el-table-column label="用户昵称" align="center" prop="username" />
         <el-table-column label="成为会员时间" align="center" prop="createdAt" width="180">
           <template #default="scope">
             <span>{{ parseTime(scope.row.createdAt) }}</span>
           </template>
         </el-table-column>
         <el-table-column label="社区名称" align="center" prop="communityName" />
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
           <template #default="scope">
             <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
           </template>
         </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 购买会员弹窗 -->
      <member-purchase-dialog
         v-model="purchaseDialogVisible"
         @submitSuccess="handlePurchaseSuccess"
      />

      <!-- 会员详情弹窗 -->
      <member-detail-dialog
         v-model="detailDialogVisible"
         :member-data="currentMember"
      />

   </div>
</template>

<script setup name="Member">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import MemberPurchaseDialog from './MemberPurchaseDialog.vue';
import MemberDetailDialog from './MemberDetailDialog.vue';
import { listMember, queryMemberNum } from '@/api/member';

const { proxy } = getCurrentInstance();
const { parseTime } = proxy;

// 加载状态
const loading = ref(true);
// 总条数
const total = ref(0);
// 会员表格数据
const memberList = ref([]);
// 可用会员数量
const availableMemberCount = ref(0);
// 弹窗相关状态
const purchaseDialogVisible = ref(false); // 控制购买会员弹窗显示
const detailDialogVisible = ref(false); // 控制会员详情弹窗显示
const currentMember = ref({}); // 当前操作（详情）的会员数据

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

/** 查询会员列表 */
function getList() {
  loading.value = true;
  listMember(queryParams).then(response => {
    if (response.success && response.data) {
      // 根据接口文档，数据在response.data.list中
      memberList.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      memberList.value = [];
      total.value = 0;
      proxy.$modal.msgError(response.message || '获取会员列表失败');
    }
    loading.value = false;
  }).catch(error => {
    console.error('获取会员列表失败:', error);
    memberList.value = [];
    total.value = 0;
    loading.value = false;
    proxy.$modal.msgError('获取会员列表失败');
  });
}

/** 获取可用会员数量 */
function getAvailableMemberCount() {
  queryMemberNum().then(response => {
    if (response.code === 200) {
      availableMemberCount.value = response.data || 0;
    } else {
      console.error('获取可用会员数量失败:', response.message);
    }
  }).catch(error => {
    console.error('获取可用会员数量失败:', error);
  });
}

/** 购买会员按钮操作 */
function handlePurchase() {
  purchaseDialogVisible.value = true;
}

/** 购买成功后的回调 */
function handlePurchaseSuccess() {
  getList(); // 刷新会员列表
  getAvailableMemberCount(); // 刷新可用会员数量
}

/** 详情按钮操作 */
function handleDetail(row) {
    currentMember.value = { ...row };
    detailDialogVisible.value = true;
}

onMounted(() => {
  getList(); // 组件挂载后获取列表数据
  getAvailableMemberCount(); // 获取可用会员数量
});

// keep-alive 组件激活时重新获取数据
onActivated(() => {
  getList(); // 组件挂载后获取列表数据
  getAvailableMemberCount(); // 获取可用会员数量
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 10px;
}
.el-tabs {
  margin-bottom: 15px;
}

.member-info-section {
  display: flex;
  align-items: center;

  .member-count-info {
    font-size: 14px;
    color: #606266;

    .count-number {
      font-weight: bold;
      color: #409eff;
      font-size: 16px;
    }
  }
}

/* 可以根据需要调整表格高度 */
.el-table {
  height: calc(100vh - 340px) !important;
}
</style>