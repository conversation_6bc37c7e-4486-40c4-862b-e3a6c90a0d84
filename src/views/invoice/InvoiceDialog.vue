<template>
  <el-dialog
    :title="title"
    v-model="open"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="invoice-dialog"
    :fullscreen="isMobile"
  >
    <el-form
      ref="invoiceFormRef"
      :model="form"
      :rules="rules"
      :label-width="isMobile ? '80px' : '100px'"
      label-position="top"
      class="invoice-form"
    >
      <el-form-item label="购方名称" prop="purchaseName">
        <el-input v-model="form.purchaseName" placeholder="请输入购方名称" />
      </el-form-item>
      <el-form-item label="购方税号" prop="purchaseTaxNumber">
        <el-input v-model="form.purchaseTaxNumber" placeholder="请输入购方税号" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { updateMerchantTaxNumber } from '@/api/login';

const emit = defineEmits(['success']);

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '500px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const open = ref(false);
const title = ref('');
const submitLoading = ref(false);
const invoiceFormRef = ref(null);

const data = reactive({
  form: {
    purchaseName: '',
    purchaseTaxNumber: '',
  },
  rules: {
    purchaseName: [{ required: true, message: '购方名称不能为空', trigger: 'blur' }],
    purchaseTaxNumber: [{ required: true, message: '购方税号不能为空', trigger: 'blur' }],
  },
});

const { form, rules } = toRefs(data);

/** 打开弹窗 */
const openDialog = (invoiceData = null) => {
  open.value = true;
  title.value = '维护开票信息';

  if (invoiceData) {
    form.value = {
      purchaseName: invoiceData.purchaseName || '',
      purchaseTaxNumber: invoiceData.purchaseTaxNumber || '',
    };
  } else {
    reset();
  }
};

/** 取消按钮 */
const cancel = () => {
  open.value = false;
  reset();
};

/** 表单重置 */
const reset = () => {
  form.value = {
    purchaseName: '',
    purchaseTaxNumber: '',
  };
  if (invoiceFormRef.value) {
    invoiceFormRef.value.resetFields();
  }
};

/** 提交按钮 */
const submitForm = async () => {
  invoiceFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true;
      try {
        const response = await updateMerchantTaxNumber(form.value);

        if (response.code === 200) {
          ElMessage.success('开票信息保存成功！');
          open.value = false;
          emit('success'); // 通知父组件刷新数据
        } else {
          ElMessage.error(response.message || '保存失败');
        }
      } catch (error) {
        console.error('保存开票信息失败:', error);
        ElMessage.error('保存失败，请稍后重试');
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.invoice-dialog {
  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.invoice-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  text-align: right;
}

// 移动端适配
@media (max-width: 768px) {
  .invoice-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 15px;
      text-align: center;

      .el-button {
        width: 120px;
        margin: 0 8px;
      }
    }
  }

  .invoice-form {
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
      line-height: 32px;
      padding-bottom: 8px;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .invoice-dialog {
    :deep(.el-dialog__footer) {
      .el-button {
        width: 100px;
        margin: 0 5px;
        font-size: 12px;
      }
    }
  }

  .invoice-form {
    :deep(.el-form-item__label) {
      font-size: 12px;
      line-height: 30px;
    }
  }
}
</style>