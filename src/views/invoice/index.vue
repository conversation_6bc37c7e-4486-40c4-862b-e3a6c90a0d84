<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Edit"
          @click="handleEdit"
        >维护开票信息</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="invoiceList">
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column label="购方名称" align="center" prop="payName" />
      <el-table-column label="购方税号" align="center" prop="taxNo" />
      <el-table-column label="开票金额" align="center" prop="taxAmount">
        <template #default="scope">
          <span>{{ scope.row.taxAmount ? scope.row.taxAmount.toFixed(2) : '0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="type">
        <template #default="scope">
          <el-tag :type="typeTagType(scope.row.type)">{{ typeText(scope.row.type) }}</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <invoice-dialog ref="invoiceDialogRef" @success="handleDialogSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';
import { getSystemConfig } from '@/api/member';
import { listInvoice } from '@/api/invoice';
import Pagination from '@/components/Pagination/index.vue';
import InvoiceDialog from './InvoiceDialog.vue';

const loading = ref(true);
const total = ref(0);
const invoiceList = ref([]);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

const invoiceInfo = ref({
  purchaseName: '',
  purchaseTaxNumber: ''
});

const invoiceDialogRef = ref(null);

/** 类型标签类型 */
const typeTagType = (type) => {
  switch (type) {
    case 1:
      return 'success';
    case 2:
      return 'info';
    default:
      return '';
  }
};

/** 类型文本 */
const typeText = (type) => {
  switch (type) {
    case 1:
      return '普通发票';
    case 2:
      return '专用发票';
    default:
      return '未知';
  }
};

/** 查询开票列表 */
const getList = async () => {
  loading.value = true;
  try {
    const response = await listInvoice(queryParams);
    if (response.code === 200) {
      invoiceList.value = response.data.list || [];
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取开票列表失败:', error);
    ElMessage.error('获取开票列表失败');
  } finally {
    loading.value = false;
  }
};

/** 获取开票信息 */
const getInvoiceInfo = async () => {
  try {
    const response = await getSystemConfig();
    if (response.code === 200 && response.data && response.data?.merchant) {
      invoiceInfo.value = {
        purchaseName: response.data.merchant.purchaseName || '',
        purchaseTaxNumber: response.data.merchant.purchaseTaxNumber || ''
      };
    }
  } catch (error) {
    console.error('获取开票信息失败:', error);
    ElMessage.error('获取开票信息失败');
  }
};

/** 编辑按钮操作 */
const handleEdit = () => {
  invoiceDialogRef.value.openDialog(invoiceInfo.value);
};

/** 弹窗成功回调 */
const handleDialogSuccess = () => {
  getInvoiceInfo(); // 刷新开票信息
};

onMounted(() => {
  getList();
  getInvoiceInfo();
});
</script>