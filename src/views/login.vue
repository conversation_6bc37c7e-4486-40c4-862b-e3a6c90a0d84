<template>
  <div class="login">
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">{{ title }}</h3>
        <el-form-item prop="mobile">
          <el-input
            v-model="loginForm.mobile"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="手机号"
          >
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter="handleLogin"
        >
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter="handleLogin"
        >
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <VerificationCode ref="verificationCodeRef" class="login-code-img" :codeLen="4" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width:100%;"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2025 ruoyi.vip All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user';
import VerificationCode from '@/components/VerificationCode/index.vue'; // 导入验证码组件

const title = import.meta.env.VITE_APP_TITLE;
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const verificationCodeRef = ref(null); // 引用验证码组件实例

const loginForm = ref({
  mobile: "18888888888",
  password: "123456",
  rememberMe: false,
  loginType: 1,
  code: "", // 重新添加 code 字段
});

// onMounted(()=>{
//   loginForm.value.code = verificationCodeRef.value.codeValue
// })

const validateVerifyCode = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入验证码'));
  } else if (verificationCodeRef.value && value.toLowerCase() !== verificationCodeRef.value.codeValue.toLowerCase()) {
    callback(new Error('验证码不正确'));
    //if (verificationCodeRef.value) {
    //  verificationCodeRef.value.changeCode(); // 刷新验证码
    //}
  } else {
    callback();
  }
};

const loginRules = {
  mobile: [{ required: true, trigger: "blur", message: "请输入您的手机号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ validator: validateVerifyCode, trigger: "blur" }] // 重新添加 code 校验
};

const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true); // 恢复验证码开关，并默认为 true
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("mobile", loginForm.value.mobile, { expires: 30 }); // 修改为 mobile
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("mobile"); // 修改为 mobile
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 准备登录参数
      const userInfo = {
        loginType: loginForm.value.loginType,
        mobile: loginForm.value.mobile,
        password: loginForm.value.password
      };
      // 调用action的登录方法
      userStore.login(userInfo).then(() => { // 传递新的 userInfo
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        router.push({ path: redirect.value || "/", query: otherQueryParams });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value && verificationCodeRef.value) {
          verificationCodeRef.value.changeCode(); // 刷新验证码
        }
      });
    }
  });
}

// 点击验证码图片时刷新
function refreshVerificationCode() {
  if (verificationCodeRef.value) {
    verificationCodeRef.value.changeCode();
  }
}

function getCookie() {
  const mobile = Cookies.get("mobile"); // 修改为 mobile
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  // loginForm 的初始结构已经包含 loginType，这里不需要重新定义整个对象
  // 只更新从 cookie 获取的字段
  loginForm.value.mobile = mobile === undefined ? loginForm.value.mobile : mobile;
  loginForm.value.password = password === undefined ? loginForm.value.password : decrypt(password);
  loginForm.value.rememberMe = rememberMe === undefined ? false : Boolean(rememberMe);
}

getCookie();
</script>

<style lang='scss' scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  z-index: 1;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
