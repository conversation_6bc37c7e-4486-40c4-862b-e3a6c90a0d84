<template>
  <el-dialog
    title="编辑商家信息"
    v-model="visible"
    :width="isMobile ? '95%' : '800px'"
    :top="isMobile ? '2vh' : '5vh'"
    append-to-body
    @close="handleClose"
    :custom-class="`merchant-edit-dialog ${isMobile ? 'mobile-dialog' : ''}`"
  >
    <el-form
      ref="merchantFormRef"
      :model="form"
      :rules="rules"
      :label-width="isMobile ? '80px' : '100px'"
      label-position="top"
      :class="{ 'mobile-form': isMobile }"
    >
      <el-row :gutter="isMobile ? 10 : 20">
        <!-- 左侧图片上传 -->
        <el-col :span="isMobile ? 24 : 10">
          <el-form-item label="商家Logo" prop="logo">
            <ImageUpload
              v-model="form.logo"
              :limit="1"
              class="logo-uploader"
            >
              <template #tip>
                <div class="el-upload__tip">
                  只能上传一张Logo图片，建议尺寸 200*200
                </div>
              </template>
            </ImageUpload>
          </el-form-item>

          <el-form-item label="封面图" prop="coverImage">
            <ImageUpload
              v-model="form.coverImage"
              :limit="1"
              class="cover-uploader"
            >
              <template #tip>
                <div class="el-upload__tip">
                  只能上传一张封面图，建议尺寸 800*400
                </div>
              </template>
            </ImageUpload>
          </el-form-item>

          <el-form-item label="广告图" prop="adUrl">
            <ImageUpload
              v-model="form.adUrl"
              :limit="1"
              class="ad-uploader"
            >
              <template #tip>
                <div class="el-upload__tip">
                  只能上传一张广告图，建议尺寸 800*400
                </div>
              </template>
            </ImageUpload>
          </el-form-item>
        </el-col>

        <!-- 右侧表单项 -->
        <el-col :span="isMobile ? 24 : 14">
          <el-form-item label="商家名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入商家名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="宣传语" prop="slogan">
            <el-input
              v-model="form.slogan"
              placeholder="请输入商家宣传语"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="详细地址" prop="address">
            <MapLocationPicker
              v-model="form.address"
              v-model:longitude="form.longitude"
              v-model:latitude="form.latitude"
              height="200px"
              @locationChange="handleLocationChange"
            />
          </el-form-item>

          <el-form-item label="商家介绍" prop="description">
            <el-input
              type="textarea"
              :rows="8"
              v-model="form.description"
              placeholder="请输入商家详细介绍"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确认保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  onMounted,
  onUnmounted
} from "vue";
import ImageUpload from "@/components/ImageUpload/index.vue";
import MapLocationPicker from "@/components/MapLocationPicker/index.vue";
import { updateMerchantInfo } from "@/api/login";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  merchantData: {
    type: Object,
    default: () => ({}),
  }
});

const emit = defineEmits(["update:modelValue", "submitSuccess"]);

const { proxy } = getCurrentInstance();

// 弹窗和表单状态
const visible = ref(false);
const merchantFormRef = ref(null);
const submitLoading = ref(false);

// 移动端检测
const isMobile = ref(false);

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// 表单数据
const form = ref({
  name: '',
  slogan: '',
  description: '',
  coverImage: '',
  adUrl: '',
  logo: '',
  address: '',
  longitude: null,
  latitude: null
});

// 表单校验规则
const rules = {
  name: [
    { required: true, message: "商家名称不能为空", trigger: "blur" },
    { min: 2, max: 50, message: "商家名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  slogan: [
    { max: 100, message: "宣传语长度不能超过 100 个字符", trigger: "blur" }
  ],
  address: [
    { required: true, message: "请在地图上选择详细地址", trigger: "blur" },
    { max: 200, message: "地址长度不能超过 200 个字符", trigger: "blur" }
  ],
  longitude: [
    { required: true, message: "请在地图上选择位置", trigger: "change" }
  ],
  latitude: [
    { required: true, message: "请在地图上选择位置", trigger: "change" }
  ],
  description: [
    { max: 500, message: "介绍长度不能超过 500 个字符", trigger: "blur" }
  ]
};

// 监听 modelValue 变化来控制内部 visible
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      nextTick(() => {
        reset();
        if (props.merchantData) {
          // 复制商家数据到表单
          form.value = {
            name: props.merchantData.name || '',
            slogan: props.merchantData.slogan || '',
            description: props.merchantData.description || '',
            coverImage: props.merchantData.coverImage || '',
            adUrl: props.merchantData.adUrl || '',
            logo: props.merchantData.logo || '',
            address: props.merchantData.address || '',
            longitude: props.merchantData.longitude || null,
            latitude: props.merchantData.latitude || null
          };
          console.log('加载商家数据到表单:', form.value);
        }
      });
    }
  },
  { immediate: true }
);

// 表单重置
function reset() {
  form.value = {
    name: '',
    slogan: '',
    description: '',
    coverImage: '',
    adUrl: '',
    logo: '',
    address: '',
    longitude: null,
    latitude: null
  };
  if (merchantFormRef.value) {
    merchantFormRef.value.resetFields();
  }
}

// 取消按钮
function cancel() {
  visible.value = false;
  emit("update:modelValue", false);
}

// 弹窗关闭时的回调
function handleClose() {
  emit("update:modelValue", false);
}

// 提交表单
function submitForm() {
  merchantFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true;
      
      try {
        const response = await updateMerchantInfo(form.value);
        
        if (response.code === 200) {
          proxy.$modal.msgSuccess("商家信息更新成功");
          visible.value = false;
          emit("update:modelValue", false);
          emit("submitSuccess");
        } else {
          proxy.$modal.msgError(response.message || "更新失败");
        }
      } catch (error) {
        console.error('更新商家信息失败:', error);
        proxy.$modal.msgError("更新失败，请稍后重试");
      } finally {
        submitLoading.value = false;
      }
    } else {
      proxy.$modal.msgError("请完善表单信息");
    }
  });
}

// 处理位置变化
function handleLocationChange(locationData) {
  console.log('位置变化:', locationData);
  // 位置数据已经通过 v-model 自动更新到 form 中
  // 这里可以添加额外的处理逻辑，比如验证字段
  if (merchantFormRef.value) {
    // 手动触发相关字段的验证
    merchantFormRef.value.validateField(['address', 'longitude', 'latitude']);
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

// 调整上传组件样式
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 110px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

// 调整表单项间距
.el-form-item {
  margin-bottom: 18px;
}

// 针对特定上传组件的提示文字样式
.logo-uploader .el-upload__tip,
.cover-uploader .el-upload__tip,
.ad-uploader .el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>

// 全局样式
<style lang="scss">
.merchant-edit-dialog {
  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-right: 0;
    padding-bottom: 10px;
  }
  .el-dialog__body {
    padding: 20px 25px;
  }
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 15px 25px;
  }

  // 移动端样式
  &.mobile-dialog {
    .el-dialog__header {
      padding: 15px 20px 10px;

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__body {
      padding: 15px 20px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 10px 20px 15px;
    }
  }
}

// 移动端表单样式
.mobile-form {
  .el-form-item {
    margin-bottom: 15px;

    .el-form-item__label {
      font-size: 14px;
      margin-bottom: 5px;
    }
  }

  // 移动端图片上传组件调整
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
    line-height: 88px;
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 80px;
    height: 80px;
  }

  // 移动端地图组件高度调整
  .map-location-picker {
    height: 150px !important;
  }

  // 移动端文本域行数调整
  .el-textarea__inner {
    min-height: 100px !important;
  }
}

// 移动端媒体查询
@media (max-width: 768px) {
  .merchant-edit-dialog {
    .el-dialog {
      margin: 0 !important;
      max-height: 95vh;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__body {
      flex: 1;
      overflow-y: auto;
      padding: 10px 15px;
    }

    .el-dialog__header {
      padding: 10px 15px 8px;
      flex-shrink: 0;
    }

    .el-dialog__footer {
      padding: 8px 15px 10px;
      flex-shrink: 0;
    }
  }

  // 移动端表单项布局
  .mobile-form {
    .el-row {
      margin: 0 !important;

      .el-col {
        padding: 0 !important;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .el-form-item {
      margin-bottom: 12px;
    }

    // 移动端按钮样式
    .dialog-footer {
      text-align: center;

      .el-button {
        width: 45%;
        margin: 0 2.5%;
      }
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .merchant-edit-dialog.mobile-dialog {
    .el-dialog {
      width: 100% !important;
      margin: 0 !important;
      border-radius: 0;
      height: 100vh;
      max-height: 100vh;
    }

    .el-dialog__header {
      padding: 8px 12px 6px;

      .el-dialog__title {
        font-size: 15px;
      }
    }

    .el-dialog__body {
      padding: 8px 12px;
    }

    .el-dialog__footer {
      padding: 6px 12px 8px;
    }
  }

  .mobile-form {
    :deep(.el-upload--picture-card) {
      width: 70px;
      height: 70px;
      line-height: 78px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 70px;
      height: 70px;
    }

    .dialog-footer {
      .el-button {
        width: 48%;
        margin: 0 1%;
        font-size: 14px;
      }
    }
  }
}
</style>
