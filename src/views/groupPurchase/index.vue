<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8" v-if="activeTab === 'goods'">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['cms:commodity:add']"
          >添加商品</el-button
        >
      </el-col>
    </el-row>

    <el-tabs v-model="activeTab" class="mb8">
      <el-tab-pane label="拼团商品" name="goods"></el-tab-pane>
      <el-tab-pane label="拼团订单" name="orders"></el-tab-pane>
    </el-tabs>

    <!-- 拼团商品表格 -->
    <el-table v-if="activeTab === 'goods'" v-loading="loading" :data="commodityList">
      <el-table-column
        label="封面"
        align="center"
        prop="coverImage"
        width="100"
      >
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.coverImage || defaultCover"
            fit="cover"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column
        label="名称"
        align="left"
        prop="name"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handlePreview(scope.row)">{{
            scope.row.name
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="原价格" align="center" prop="price" width="90">
        <template #default="scope">
          <span>¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成团价格" align="center" prop="discountPrice" width="90">
        <template #default="scope">
          <span>¥{{ scope.row.discountPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成团人数" align="center" prop="numGroup" width="90">
        <template #default="scope">
          <span>{{ scope.row.numGroup }}人</span>
        </template>
      </el-table-column>
      <el-table-column label="拼团状态" align="center" prop="isInGroup" width="90">
        <template #default="scope">
          <el-tag v-if="scope.row.isInGroup === 1" type="success">拼团中</el-tag>
          <el-tag v-else type="info">未拼团</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="还差人数" align="center" prop="groupDifference" width="90">
        <template #default="scope">
          <span v-if="scope.row.isInGroup === 1">{{ scope.row.groupDifference }}人</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="浏览量" align="center" prop="viewNum" width="80">
        <template #default="scope">
          <span>{{ scope.row.viewNum || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">上架</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="warning">下架</el-tag>
          <el-tag v-else-if="scope.row.status === 3" type="primary">预售</el-tag>
          <el-tag v-else-if="scope.row.status === 4" type="danger">售罄</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createdAt"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handlePreview(scope.row)">查看详情</el-button>
          <!-- 根据状态显示上架或下架按钮 -->
          <el-button
            v-if="scope.row.status === 1"
            link
            type="warning"
            @click="handleToggleStatus(scope.row)"
            v-hasPermi="['cms:commodity:status']"
          >
            下架
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            link
            type="success"
            @click="handleToggleStatus(scope.row)"
            v-hasPermi="['cms:commodity:status']"
          >
            上架
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cms:commodity:edit']"
          >编辑</el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cms:commodity:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 拼团订单表格 -->
    <el-table v-else-if="activeTab === 'orders'" v-loading="loading" :data="orderList">
      <el-table-column
        label="封面"
        align="center"
        prop="coverImage"
        width="100"
      >
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.coverImage || defaultCover"
            fit="cover"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column
        label="商品名称"
        align="left"
        prop="name"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="原价格" align="center" prop="price" width="90">
        <template #default="scope">
          <span>¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成团价格" align="center" prop="discountPrice" width="90">
        <template #default="scope">
          <span>¥{{ scope.row.discountPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成团人数" align="center" prop="numGroup" width="90">
        <template #default="scope">
          <span>{{ scope.row.numGroup }}人</span>
        </template>
      </el-table-column>
      <el-table-column label="还差人数" align="center" prop="groupDifference" width="90">
        <template #default="scope">
          <span>{{ scope.row.groupDifference }}人</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="warning">待成团</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="success">已成团</el-tag>
          <el-tag v-else-if="scope.row.status === 3" type="danger">已过期</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createdAt"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleViewUsers(scope.row)"
            v-hasPermi="['cms:groupPurchase:view']"
          >
            查看成团用户
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="currentTotal"
      v-model:page="currentPageNum"
      v-model:limit="currentPageSize"
      @pagination="handlePagination"
    />

    <!-- 商品表单弹窗 -->
    <commodity-form
      v-model="dialogVisible"
      :commodity-data="currentCommodity"
      :is-edit="isEditMode"
      :is-preview="isPreviewMode"
      @submitSuccess="getList"
    />

    <!-- 成团用户弹窗 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="`${currentOrder.name} - 成团用户列表`"
      width="800px"
      destroy-on-close
    >
      <el-table :data="userList" style="width: 100%">
        <el-table-column
          label="头像"
          align="center"
          width="80"
        >
          <template #default="scope">
            <el-avatar
              :size="40"
              :src="scope.row.avatar"
              :alt="scope.row.nickName"
            >
              {{ scope.row.nickName?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column
          label="用户昵称"
          align="center"
          prop="nickName"
          width="120"
        />
        <el-table-column
          label="社区名称"
          align="center"
          prop="communityName"
          width="150"
        />
        <el-table-column
          label="拼团时间"
          align="center"
          prop="groupTime"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.groupTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GroupPurchase">
import { ref, reactive, onMounted, onActivated, getCurrentInstance, watch, computed } from "vue";
import CommodityForm from "./GroupPurchaseGoodsForm.vue"; // 引入拼团商品表单组件
import { listGroupSharingGoods, listGroupSharingOrders, listGroupSharingUsers, updateCommodityStatus, deleteCommodity } from "@/api/commodity"; // 商品API
import defaultCover from "@/assets/images/profile.jpg"; // 默认封面图路径

const { proxy } = getCurrentInstance();
const { parseTime } = proxy; // 假设 parseTime 在全局挂载

// --- 状态定义 ---
const loading = ref(false);
const goodsTotal = ref(0); // 商品总数
const orderTotal = ref(0); // 订单总数
const commodityList = ref([]); // 商品列表
const orderList = ref([]); // 订单列表
const userList = ref([]); // 用户列表
const dialogVisible = ref(false);
const userDialogVisible = ref(false); // 用户弹窗
const isEditMode = ref(false);
const isPreviewMode = ref(false); // 预览模式
const currentCommodity = ref({}); // 当前商品数据
const currentOrder = ref({}); // 当前订单数据
const activeTab = ref("goods"); // 当前选中的 Tab，默认为拼团商品

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  typeId: 2, // 拼团商品类型
  merchantId: undefined, // 商家ID，根据实际情况设置
});

// 订单查询参数
const orderQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  goodId: undefined, // 商品ID
});

// 用户查询参数
const userQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderId: undefined, // 订单ID
});

// 计算属性：当前页码
const currentPageNum = computed({
  get: () => activeTab.value === 'goods' ? queryParams.pageNum : orderQueryParams.pageNum,
  set: (val) => {
    if (activeTab.value === 'goods') {
      queryParams.pageNum = val;
    } else {
      orderQueryParams.pageNum = val;
    }
  }
});

// 计算属性：当前页大小
const currentPageSize = computed({
  get: () => activeTab.value === 'goods' ? queryParams.pageSize : orderQueryParams.pageSize,
  set: (val) => {
    if (activeTab.value === 'goods') {
      queryParams.pageSize = val;
    } else {
      orderQueryParams.pageSize = val;
    }
  }
});

// 计算属性：当前总数
const currentTotal = computed(() => {
  return activeTab.value === 'goods' ? goodsTotal.value : orderTotal.value;
});

/** 查询拼团商品列表 */
function getList() {
  if(loading.value) return
  loading.value = true;
  listGroupSharingGoods(queryParams)
    .then((response) => {
      if (response.code === 200) { // 根据新API文档，成功响应code为0
        commodityList.value = response.data.list;
        goodsTotal.value = response.data.total;
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 查询拼团订单列表 */
function getOrderList() {
  if(loading.value) return
  loading.value = true;
  listGroupSharingOrders(orderQueryParams)
    .then((response) => {
      if (response.code === 200) {
        orderList.value = response.data.list;
        orderTotal.value = response.data.total;
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}



/** 新增按钮操作 */
function handleAdd() {
  isEditMode.value = false;
  isPreviewMode.value = false;
  currentCommodity.value = {}; // 清空当前商品数据
  dialogVisible.value = true; // 打开弹窗
}

/** 修改按钮操作 */
function handleUpdate(row) {
  currentCommodity.value = { ...row }; // 填充当前商品数据
  isEditMode.value = true;
  isPreviewMode.value = false;
  dialogVisible.value = true; // 打开弹窗
}

/** 上/下架按钮操作 */
function handleToggleStatus(row) {
  const text = row.status === 1 ? "下架" : "上架";
  const newStatus = row.status === 1 ? 2 : 1;
  proxy.$modal
    .confirm(`确认要"${text}"商品"${row.name}"吗？`)
    .then(() => {
      return updateCommodityStatus({ id: row.id, status: newStatus });
    })
    .then(() => {
      getList(); // 重新加载列表
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(() => {
      // 可选：如果用户取消，恢复按钮状态或给出提示
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除商品名称为"' + row.name + '"的数据项？')
    .then(function () {
      return deleteCommodity(row.id);
    })
    .then((response) => {
      if (response.code === 200) { // 根据API文档，成功响应code为0
        getList(); // 重新加载列表
        proxy.$modal.msgSuccess("删除成功");
      } else {
        proxy.$modal.msgError(response.message || "删除失败");
      }
    })
    .catch(() => {});
}

/** 预览按钮操作 */
function handlePreview(row) {
  currentCommodity.value = { ...row }; // 填充当前商品数据
  isEditMode.value = false; // 不是编辑模式
  isPreviewMode.value = true; // 设置为预览模式
  dialogVisible.value = true; // 打开弹窗
}

/** 查看成团用户 */
function handleViewUsers(row) {
  currentOrder.value = { ...row };
  userQueryParams.orderId = row.groupSharingId; // 使用groupSharingId作为订单ID
  userQueryParams.pageNum = 1;
  getUserList();
  userDialogVisible.value = true;
}

/** 查询拼团用户列表 */
function getUserList() {
  listGroupSharingUsers(userQueryParams)
    .then((response) => {
      if (response.code === 200) {
        userList.value = response.data.list;
      }
    })
    .catch(() => {
      proxy.$modal.msgError("获取用户列表失败");
    });
}

/** 分页处理 */
function handlePagination() {
  if (activeTab.value === 'goods') {
    getList();
  } else if (activeTab.value === 'orders') {
    getOrderList();
  }
}

// 监听tab切换
watch(
  () => activeTab.value,
  (newTab) => {
    // 切换tab时重置对应的页码
    if (newTab === 'goods') {
      queryParams.pageNum = 1;
      getList(); // 获取商品数据
    } else if (newTab === 'orders') {
      orderQueryParams.pageNum = 1;
      getOrderList(); // 获取订单数据
    }
  },
  { immediate: false } // 不立即执行，避免与onMounted重复
);

/** 根据当前tab加载对应数据 */
function loadCurrentTabData() {
  if (activeTab.value === 'goods') {
    getList();
  } else if (activeTab.value === 'orders') {
    getOrderList();
  }
}

onMounted(() => {
  loadCurrentTabData(); // 组件挂载后获取对应tab的数据
});

// keep-alive 组件激活时重新获取数据
onActivated(() => {
  loadCurrentTabData(); // 每次进入页面都获取最新数据
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 10px;
}
.el-table {
  /* 调整表格高度，根据实际页面布局可能需要微调 */
  height: calc(100vh - 340px) !important;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 20px;
}
.el-tabs {
  // 可能需要调整 tabs 的样式，例如下边距
  margin-bottom: 15px;
}
</style>
