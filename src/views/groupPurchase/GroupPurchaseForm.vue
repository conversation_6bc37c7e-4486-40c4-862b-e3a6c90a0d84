<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="900px"
    top="5vh"
    append-to-body
    @close="handleClose"
    custom-class="group-purchase-dialog"
  >
    <template #title>
      <div class="dialog-title">
        <span v-if="isPreview">拼团详情</span> 
        <span v-else-if="isEdit">编辑拼团</span> 
        <span v-else>新增拼团</span> 
      </div>
    </template>

    <el-alert v-if="!isPreview"
      title="注意事项：请确保拼团信息真实有效，符合平台规定。"
      type="warning"
      show-icon
      :closable="false"
      class="mb10"
    />

    <el-form
      ref="groupPurchaseFormRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      label-position="top"
      :disabled="isPreview"
    >
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="封面图" prop="coverUrl">
             <ImageUpload
                :action="uploadUrl"
                :headers="headers"
                list-type="picture-card"
                :limit="1"
                :on-success="handleCoverSuccess"
                :before-upload="beforeUpload"
                :on-remove="handleCoverRemove"
                :file-list="coverFileList"
                class="cover-uploader"
                :disabled="isPreview"
              >
                <el-icon v-if="!isPreview"><Plus /></el-icon>
                 <template #tip v-if="!isPreview">
                    <div class="el-upload__tip">
                      只能上传一张封面图, 建议尺寸 XXX*XXX
                    </div>
                  </template>
              </ImageUpload>
          </el-form-item>

          <el-form-item label="成团人数" prop="requiredPeople">
            <el-input-number
              v-model="form.requiredPeople"
              :min="1"
              placeholder="请输入成团人数"
              controls-position="right"
              style="width: 100%;"
            />
             <div class="el-form-item__info" v-if="!isPreview">
                <el-icon><InfoFilled /></el-icon> 至少需要多少人参与才算拼团成功
             </div>
          </el-form-item>

          <el-form-item label="原价格" prop="originalPrice">
            <el-input-number
              v-model="form.originalPrice"
              :precision="2"
              :step="1"
              :min="0"
              placeholder="请输入价格"
              controls-position="right"
              style="width: 100%;"
            />
          </el-form-item>

          <el-form-item label="拼团价格" prop="groupPrice">
             <el-input-number
              v-model="form.groupPrice"
              :precision="2"
              :step="1"
              :min="0"
              placeholder="请输入优惠价格"
              controls-position="right"
              style="width: 100%;"
            />
          </el-form-item>

           <el-form-item label="总费用" prop="totalCost">
             <el-input-number
               v-model="calculatedTotalCost"
               :precision="2"
               :min="0"
               placeholder="自动计算"
               controls-position="right"
               style="width: 100%;"
               :disabled="true"
             />
              <div class="el-form-item__info">
                 <el-icon><InfoFilled /></el-icon> 成团人数 x 1元 (示例计算)
              </div>
           </el-form-item>

        </el-col>

        <el-col :span="14">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入商品名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="描述" prop="description">
             <Editor v-model="form.description" :min-height="350" :readOnly="isPreview"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="!isPreview" @click="cancel">取 消</el-button>
        <el-button v-if="!isPreview" type="primary" @click="submitForm">{{ isEdit ? '确认修改' : '确认发布' }}</el-button>
        <el-button v-else type="primary" @click="cancel">关 闭</el-button>
      </div>
    </template>

    <el-dialog
      v-model="previewVisible"
      title="封面预览"
      width="600px"
      append-to-body
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  computed
} from "vue";
import { getToken } from "@/utils/auth"; // 获取token用于上传请求头
import { Plus, ArrowRight, InfoFilled } from "@element-plus/icons-vue"; // 引入图标
import ImageUpload from "@/components/ImageUpload/index.vue";
import Editor from '@/components/Editor/index.vue'; // 引入富文本编辑器
// import { addCommodity, updateCommodity, getCommodityDetail } from "@/api/commodity"; // 假设的API路径

const props = defineProps({
  modelValue: { // 控制弹窗显示/隐藏
    type: Boolean,
    required: true,
  },
  groupPurchaseData: { // 修改 prop 名
    type: Object,
    default: () => ({})
  },
  groupPurchaseId: { // 修改 prop 名
    type: [Number, String],
    default: null,
  },
  isEdit: { // 是否为编辑状态
    type: Boolean,
    default: false,
  },
  isPreview: { // 是否为预览模式
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(["update:modelValue", "submitSuccess"]);

const { proxy } = getCurrentInstance(); // 获取全局方法

// --- 上传相关配置 ---
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传地址
const headers = ref({ Authorization: "Bearer " + getToken() }); // 上传请求头
const coverFileList = ref([]); // 封面图文件列表
const maxFileSize = 5; // 文件大小限制 (MB)
const previewVisible = ref(false); // 图片预览弹窗
const previewImageUrl = ref(""); // 预览图片URL

// --- 弹窗和表单状态 ---
const visible = ref(false);
const title = ref(""); // 弹窗标题 (通过 #title 插槽实现双标题)
const form = ref({});
const groupPurchaseFormRef = ref(null); // 修改 ref 名

// 计算总费用 (示例)
const calculatedTotalCost = computed(() => {
    const people = form.value.requiredPeople || 0;
    // 假设每人1元费用，实际计算方式根据业务确定
    return (people * 1.00).toFixed(2);
});

// 监听 modelValue 变化来控制内部 visible
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      // 弹窗打开时，根据状态设置标题和表单数据
      if (props.isPreview) {
        title.value = "拼团详情"; // 修改标题
      } else {
        title.value = props.isEdit ? "编辑拼团" : "新增拼团"; // 修改标题
      }
      // 使用 nextTick 确保 DOM 更新后再重置表单和赋值
      nextTick(() => {
        reset(); // 先重置表单
        // 编辑或预览模式下填充数据 (使用传入的 groupPurchaseData)
        if ((props.isEdit || props.isPreview) && props.groupPurchaseData && Object.keys(props.groupPurchaseData).length > 0) {
            form.value = { ...props.groupPurchaseData }; // 使用传入的数据填充表单
            // 回显封面图
            coverFileList.value = form.value.coverUrl
              ? [{ name: "cover", url: form.value.coverUrl, status: 'success' }]
              : [];
             // 预览模式下清除校验状态
             if (props.isPreview && groupPurchaseFormRef.value) {
                groupPurchaseFormRef.value.clearValidate();
             }
        }
        // --- 保留原始预览逻辑的注释，以备将来需要API调用 ---
        // else if (props.isPreview && props.groupPurchaseId) {
          // // 这里应该调用API获取拼团详情
          // // getGroupPurchase(props.groupPurchaseId).then(response => {
          // //   form.value = response.data;
          //   // 回显封面图等...
          //   // ...
          //   // 清除校验状态
          //   if (groupPurchaseFormRef.value) {
          //      groupPurchaseFormRef.value.clearValidate();
          //   }
          // }).catch(() => {
          //    proxy.$modal.msgError("获取拼团详情失败");
          // });
        // }
        else {
          // 新增时清空文件列表
          coverFileList.value = [];
        }
      });
    }
  },
  { immediate: true }
);

// 表单校验规则
// 预览模式下不应用规则
const rules = computed(() => props.isPreview ? {} : {
    coverUrl: [
      { required: true, message: "请上传封面图", trigger: "change" }
    ],
    name: [{ required: true, message: "拼团名称不能为空", trigger: "blur" }],
    description: [{ required: true, message: "拼团描述不能为空", trigger: "blur" }],
    requiredPeople: [
        { required: true, message: "成团人数不能为空", trigger: "blur" },
        { type: 'integer', min: 1, message: '成团人数必须大于0', trigger: 'blur' }
    ],
    originalPrice: [ // 修改 prop 名
        { required: true, message: "原价格不能为空", trigger: "blur" },
        { type: 'number', message: '原价格必须为数字值'}
    ],
    groupPrice: [ // 修改 prop 名
        { required: true, message: "拼团价格不能为空", trigger: "blur" },
        { type: 'number', message: '拼团价格必须为数字值'}
        // 可以添加校验：拼团价格不能高于原价格
        // { validator: validateGroupPrice, trigger: 'blur' }
    ],
});

// 表单重置
function reset() {
  form.value = {
    id: undefined, // 假设主键是 id
    name: undefined,
    description: undefined,
    coverUrl: undefined,
    requiredPeople: undefined, // 新增
    originalPrice: undefined, // 修改
    groupPrice: undefined, // 修改
    // totalCost: undefined, // 总费用是计算所得，不需要重置输入
    // 其他字段...
  };
  coverFileList.value = []; // 清空封面文件列表
  if (groupPurchaseFormRef.value) { // 修改 ref 名
    groupPurchaseFormRef.value.resetFields(); // 重置校验状态
    // 手动重置富文本编辑器内容
    form.value.description = '';
  }
}

// --- 上传处理函数 ---

// 上传前校验
const beforeUpload = file => {
  const isLtSize = file.size / 1024 / 1024 < maxFileSize;
  if (!isLtSize) {
    proxy.$modal.msgError(`上传文件大小不能超过 ${maxFileSize}MB!`);
  }
  // 可以添加文件类型校验
  // const isTypeValid = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type);
  // if (!isTypeValid) {
  //   proxy.$modal.msgError('上传文件格式不正确!');
  // }
  // return isLtSize && isTypeValid;
  return isLtSize;
};

// 封面上传成功
const handleCoverSuccess = (response, uploadFile) => {
  if (response.code === 200) {
    form.value.coverUrl = response.data.url; // 保存URL
    coverFileList.value = [uploadFile]; // 更新文件列表（确保只有一个）
    // 手动触发封面图校验
    if (groupPurchaseFormRef.value) { // 修改 ref 名
      groupPurchaseFormRef.value.validateField("coverUrl");
    }
    proxy.$modal.msgSuccess("封面上传成功");
  } else {
    proxy.$modal.msgError(response.msg || "封面上传失败");
    // 从列表中移除失败的文件
    coverFileList.value = coverFileList.value.filter(
      f => f.uid !== uploadFile.uid
    );
     // 上传失败时清空表单中的 URL 并重新校验
    form.value.coverUrl = undefined;
    if (groupPurchaseFormRef.value) { // 修改 ref 名
      groupPurchaseFormRef.value.validateField("coverUrl");
    }
  }
};

// 封面移除
const handleCoverRemove = (uploadFile, uploadFiles) => {
  form.value.coverUrl = undefined;
  coverFileList.value = [];
  // 手动触发封面图校验
  if (groupPurchaseFormRef.value) { // 修改 ref 名
    groupPurchaseFormRef.value.validateField("coverUrl");
  }
};

// 封面图片预览 (如果 ImageUpload 组件支持 on-preview)
const handlePictureCardPreview = uploadFile => {
  previewImageUrl.value = uploadFile.url;
  previewVisible.value = true;
};


// --- 弹窗控制 ---

// 取消按钮
function cancel() {
  visible.value = false;
  emit("update:modelValue", false); // 更新父组件的 v-model
}

// 弹窗关闭时的回调
function handleClose() {
  emit("update:modelValue", false); // 确保关闭时同步状态
}

// 提交按钮
function submitForm() {
  // 预览模式下不执行提交
  if (props.isPreview) {
     cancel(); // 直接关闭
     return;
   }

  groupPurchaseFormRef.value.validate(valid => { // 修改 ref 名
    if (valid) {
      // 准备提交的数据 (可以添加 totalCost 如果后端需要)
      const submitData = { ...form.value };
      // submitData.totalCost = calculatedTotalCost.value; // 如果需要提交计算值

      // 模拟API调用
      const apiCall = props.isEdit
        ? Promise.resolve({code: 200, msg: '修改拼团成功'}) // updateGroupPurchase(submitData)
        : Promise.resolve({code: 200, msg: '新增拼团成功'}); // addGroupPurchase(submitData)

      apiCall
        .then((res) => {
          // 假设成功 code 为 200
          if (res.code === 200) {
             proxy.$modal.msgSuccess(props.isEdit ? "修改拼团成功" : "新增拼团成功"); // 修改提示
             visible.value = false;
             emit("update:modelValue", false); // 关闭弹窗
             emit("submitSuccess"); // 通知父组件提交成功，以便刷新列表
          } else {
             proxy.$modal.msgError(res.msg || "操作失败");
          }
        })
        .catch((err) => {
          // 处理API调用或其他错误
          console.error("Submit error:", err);
          proxy.$modal.msgError("操作失败");
        });
    } else {
        proxy.$modal.msgError('表单校验失败，请检查输入项'); // 优化提示
        // 可以添加滚动到第一个错误项的逻辑
        // scrollToErrorField();
        return false;
    }
  });
}

// 可选：滚动到第一个错误字段
// function scrollToErrorField() {
//   nextTick(() => {
//     const isError = document.querySelector('.is-error');
//     if (isError) {
//       isError.scrollIntoView({ behavior: 'smooth', block: 'center' });
//     }
//   });
// }

</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
}

// 模拟选择框右侧箭头样式
.select-with-arrow {
  width: 100%;
  :deep(.el-input__wrapper) {
    padding-right: 30px; /* 为图标留出空间 */
  }
  :deep(.el-input__suffix) {
    right: 5px;
    /* 隐藏默认箭头 */
    .el-select__caret {
      display: none;
    }
  }
  .el-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--el-input-icon-color, var(--el-text-color-placeholder));
    cursor: pointer; // 添加手型指针，模拟可点击
  }
}

// 调整上传组件样式
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 110px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

// 调整标签位置为顶部对齐
:deep(.el-form-item__label) {
  // margin-bottom: 0; // 可能不需要，取决于Element Plus版本和全局设置
  // line-height: normal;
  // padding: 0 !important; // 覆盖element plus默认padding
}

// 调整表单项间距
.el-form-item {
  margin-bottom: 18px; // 调整表单项之间的垂直间距
}

// 优惠价格提示信息样式
.el-form-item__info {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    display: flex;
    align-items: center;
    .el-icon {
        margin-right: 4px;
    }
}


// 调整dialog footer的样式
.dialog-footer {
  text-align: right;
}

// 针对特定上传组件的提示文字样式
.cover-uploader .el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

// 富文本编辑器样式调整 (如果需要)
:deep(.editor-container) {
    // border: 1px solid #dcdfe6; // 示例：添加边框
}
</style>

// 全局样式，用于修改 el-dialog 内部结构
<style lang="scss">
.group-purchase-dialog { // 修改 class
  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-right: 0; // 覆盖默认 margin
    padding-bottom: 10px;
  }
  .el-dialog__body {
    padding: 20px 25px; // 调整内容区域内边距
  }
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 15px 25px;
  }
  // 修复 InputNumber 控件按钮在某些情况下的样式问题
  .el-input-number .el-input__inner {
    text-align: left;
  }
}
</style>