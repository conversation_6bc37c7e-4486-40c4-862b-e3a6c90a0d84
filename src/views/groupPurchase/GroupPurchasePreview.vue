<template>
  <el-dialog
    title="详情"
    :model-value="visible"
    width="700px"
    append-to-body
    @close="handleClose"
  >
    <div v-if="groupPurchaseData" class="preview-container">
      <!-- 状态 Tabs (根据需要决定是否保留) -->
      <!-- <el-tabs v-model="activeTab" class="mb8">
        <el-tab-pane label="未成团" name="pending"></el-tab-pane>
        <el-tab-pane label="已成团" name="completed"></el-tab-pane>
        <el-tab-pane label="已失效" name="expired"></el-tab-pane>
      </el-tabs> -->

      <div class="basic-info mb15">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-image
              style="width: 100px; height: 100px"
              :src="groupPurchaseData.coverUrl || defaultCover"
              fit="cover"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </el-col>
          <el-col :span="18">
            <h4>{{ groupPurchaseData.name }}</h4>
            <el-row :gutter="10" class="info-row">
              <el-col :span="8">原价格: ¥{{ groupPurchaseData.originalPrice?.toFixed(2) }}</el-col>
              <el-col :span="8">拼团价格: ¥{{ groupPurchaseData.groupPrice?.toFixed(2) }}</el-col>
              <el-col :span="8">成团人数: {{ groupPurchaseData.requiredPeople }}</el-col>
            </el-row>
            <el-row :gutter="10" class="info-row">
              <el-col :span="8">已拼团人数: {{ groupPurchaseData.currentPeople }}</el-col>
              <el-col :span="8" v-if="groupPurchaseData.remainingTime">
                剩余有效期: <span :style="{ color: groupPurchaseData.remainingTimeColor || 'inherit' }">{{ groupPurchaseData.remainingTime }}</span>
              </el-col>
               <el-col :span="8" v-else-if="groupPurchaseData.completedTime">
                成团时间: {{ parseTime(groupPurchaseData.completedTime) }}
              </el-col>
               <el-col :span="8" v-else-if="groupPurchaseData.expiredTime">
                失效时间: {{ parseTime(groupPurchaseData.expiredTime) }}
              </el-col>
              <el-col :span="8">创建时间: {{ parseTime(groupPurchaseData.createTime) }}</el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>

      <div class="participants-info">
        <h4>参与用户</h4>
        <el-table :data="participantsData" style="width: 100%">
          <el-table-column label="用户头像" width="100" align="center">
             <template #default="scope">
                <el-avatar :size="40" :src="scope.row.avatar || defaultAvatar"></el-avatar>
             </template>
          </el-table-column>
          <el-table-column prop="nickname" label="用户昵称" />
          <el-table-column prop="joinTime" label="拼团时间">
             <template #default="scope">
                <span>{{ parseTime(scope.row.joinTime) }}</span>
             </template>
          </el-table-column>
          <el-table-column prop="communityName" label="社区名称" />
        </el-table>
        <!-- TODO: 添加分页 -->
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Picture } from "@element-plus/icons-vue";
import defaultCover from "@/assets/images/profile.jpg"; // 假设的默认封面图路径
import defaultAvatar from "@/assets/images/profile.jpg"; // 假设的默认头像路径
import { parseTime } from "@/utils/ruoyi"; // 引入时间格式化工具

const props = defineProps({
  modelValue: Boolean, // 控制弹窗显示
  groupPurchaseData: Object, // 拼团数据
});

const emit = defineEmits(['update:modelValue']);

const visible = ref(false);
// const activeTab = ref('pending'); // 如果需要 tab

// 模拟参与用户数据，实际应从API获取
const participantsData = ref([
    { id: 1, avatar: '', nickname: 'u_315413', joinTime: '2022-03-05 14:22', communityName: '碧海湾二期' },
    { id: 2, avatar: '', nickname: 'u_315413', joinTime: '2022-03-05 14:22', communityName: '双月港湾三期' },
    // ...更多用户
]);

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal && props.groupPurchaseData) {
    // activeTab.value = props.groupPurchaseData.status || 'pending'; // 如果需要 tab
    // TODO: 根据 groupPurchaseData.id 获取参与用户列表
    console.log("Previewing:", props.groupPurchaseData);
    // fetchParticipants(props.groupPurchaseData.id);
  }
});

// function fetchParticipants(id) {
//   // 模拟 API 调用
//   console.log(`Fetching participants for group purchase ID: ${id}`);
//   // 实际调用 API 获取数据并更新 participantsData.value
// }

function handleClose() {
  emit('update:modelValue', false);
}

</script>

<style lang="scss" scoped>
.preview-container {
  padding: 0 20px;
}
.mb8 {
  margin-bottom: 8px;
}
.mb15 {
    margin-bottom: 15px;
}
.basic-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
  }
  .info-row {
    margin-bottom: 8px;
    color: #606266;
    font-size: 14px;
    &:last-child {
        margin-bottom: 0;
    }
  }
}
.participants-info {
   h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 16px;
  }
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
.el-avatar {
    background: var(--el-fill-color-light);
}
</style>