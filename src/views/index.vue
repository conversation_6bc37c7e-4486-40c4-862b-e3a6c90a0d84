<template>
  <div class="dashboard-container">
    <!-- 商家信息 -->
    <el-card class="box-card merchant-info" shadow="never">
      <el-row :gutter="30">
        <!-- 左侧 Logo -->
        <el-col :xs="24" :sm="4" :md="3" class="merchant-logo-col">
          <div class="merchant-logo-placeholder" v-if="!merchantInfo?.logo">
            商家Logo
          </div>
          <el-image
            v-else
            :src="merchantInfo.logo"
            class="merchant-logo"
            fit="cover"
            :preview-src-list="[merchantInfo.logo]"
          />
        </el-col>

        <!-- 中间详细信息 -->
        <el-col :xs="24" :sm="14" :md="15">
          <div class="merchant-details">
            <h3 class="merchant-name">{{ merchantInfo.name || "商家名称" }}</h3>
            <el-descriptions
              :column="1"
              size="small"
              class="merchant-descriptions"
            >
              <el-descriptions-item
                label="商家ID"
                label-class-name="info-label"
                >{{ merchantInfo.id || "--" }}</el-descriptions-item
              >
              <el-descriptions-item
                label="宣传语"
                label-class-name="info-label"
                >{{ merchantInfo.slogan || "--" }}</el-descriptions-item
              >
              <el-descriptions-item
                label="详细地址"
                label-class-name="info-label"
                >{{ merchantInfo.address || "--" }}</el-descriptions-item
              >
              <el-descriptions-item
                label="商家介绍"
                label-class-name="info-label"
                >{{ merchantInfo.description || "--" }}</el-descriptions-item
              >
              <el-descriptions-item
                label="会员状态"
                label-class-name="info-label"
              >
                <el-tag
                  :type="merchantInfo.isMember === 1 ? 'success' : 'info'"
                >
                  {{ merchantInfo.isMember === 1 ? "是会员" : "非会员" }}
                </el-tag>
                <span style="margin: 0 10px">入驻状态</span>
                <el-tag
                  :type="merchantInfo.isJoin === 1 ? 'success' : 'warning'"
                >
                  {{ merchantInfo.isJoin === 1 ? "已入驻" : "未入驻" }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>

        <!-- 右侧操作链接和图片 -->
        <el-col :xs="24" :sm="6" :md="6">
          <div class="merchant-actions">
            <el-link type="primary" :underline="false" @click="openEditDialog"
              >编辑资料</el-link
            >
            <!-- 修改密码链接 -->
            <el-link
              type="primary"
              :underline="false"
              @click="openPasswordDialog"
              >修改密码</el-link
            >
          </div>
          <el-row :gutter="10" class="merchant-images">
            <el-col :span="12">
              <div class="image-placeholder" v-if="!merchantInfo.coverImage">
                封面图
              </div>
              <el-image
                v-else
                :src="merchantInfo.coverImage"
                class="merchant-image"
                fit="cover"
                :preview-src-list="[merchantInfo.coverImage]"
              />
            </el-col>
            <el-col :span="12">
              <div class="image-placeholder" v-if="!merchantInfo.adUrl">
                广告图
              </div>
              <el-image
                v-else
                :src="merchantInfo.adUrl"
                class="merchant-image"
                fit="cover"
                :preview-src-list="[merchantInfo.adUrl]"
              />
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据统计 -->
    <div class="data-stats">
      <div class="stats-grid">
        <el-card
          shadow="hover"
          class="stat-card"
          v-for="(item, index) in statsData"
          :key="index"
        >
          <div class="stat-title">{{ item.title }}</div>
          <div class="stat-value">{{ item.value }}</div>
        </el-card>
      </div>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :xs="24" :sm="24" :md="12">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span
                ><i
                  class="el-icon-data-line"
                  style="color: #409eff; margin-right: 5px"
                ></i
                >每天收入折线图</span
              >
              <el-date-picker
                v-model="dateRange1"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                style="width: 240px"
              />
            </div>
          </template>
          <div ref="incomeChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span
                ><i
                  class="el-icon-data-line"
                  style="color: #409eff; margin-right: 5px"
                ></i
                >每天店铺浏览折线图</span
              >
              <el-date-picker
                v-model="dateRange2"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                style="width: 240px"
              />
            </div>
          </template>
          <div ref="browseChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 修改密码弹窗 -->
    <el-dialog
      title="修改密码"
      v-model="passwordDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
      @close="resetPasswordForm"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="passwordForm.phone"
            placeholder="请输入手机号"
            maxlength="11"
            @input="handlePhoneInput"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="passwordForm.password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="验证码" prop="smsCode">
          <div style="display: flex; gap: 10px">
            <el-input
              v-model="passwordForm.smsCode"
              placeholder="请输入短信验证码"
              style="flex: 1"
            />
            <el-button
              type="primary"
              :disabled="smsCountdown > 0 || !passwordForm.phone || smsLoading"
              :loading="smsLoading"
              @click="handleSendSmsCode"
              style="width: 100px"
            >
              {{
                smsCountdown > 0 ? `重新发送(${smsCountdown}s)` : "发送验证码"
              }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePasswordSubmit"
            >确认修改</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 商家信息编辑弹窗 -->
    <MerchantEditForm
      v-model="editDialogVisible"
      :merchant-data="merchantInfo"
      @submit-success="handleEditSuccess"
    />
  </div>
</template>

<script setup name="Index">
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  nextTick,
  computed,
  watch,
} from "vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus"; // 引入 ElMessage
import useUserStore from "@/store/modules/user";
import { changePassword, sendSmsCode } from "@/api/login";
import MerchantEditForm from "@/views/merchant/MerchantEditForm.vue";
import { getStatistic, getStatisticSum } from "@/api/report";

// --- 数据定义 ---
const userStore = useUserStore();

// 商家信息
const merchantInfo = computed(() => userStore.merchantInfo || {});

// 统计汇总数据
const statisticSumData = ref({});

// 计算统计卡片数据
const statsData = computed(() => {
  const data = statisticSumData.value;
  return [
    { title: "累计收入总额", value: data.totalAmount ? data.totalAmount.toFixed(2) : "0.00" },
    { title: "当日收入总额", value: data.inComeAmount ? data.inComeAmount.toFixed(2) : "0.00" },
    { title: "总提现金额", value: data.totalPayOutAmount ? data.totalPayOutAmount.toFixed(2) : "0.00" },
    { title: "钱包余额", value: data.balance ? data.balance.toFixed(2) : "0.00" },
    { title: "当日商品浏览数量", value: data.goodBrowsCount ? data.goodBrowsCount.toString() : "0" },
    { title: "当日店铺浏览数量", value: data.shopBrowsCount ? data.shopBrowsCount.toString() : "0" },
    { title: "会员总人数", value: data.memberCount ? data.memberCount.toString() : "0" },
  ];
});

// 获取今日日期范围（开始和结束都是今天）
const getTodayRange = () => {
  const today = new Date();
  return [today, today];
};

const dateRange1 = ref(getTodayRange());
const dateRange2 = ref(getTodayRange());

// 统计数据
const statisticData = ref([]);

// 获取今日日期字符串
const getTodayDateString = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 格式化日期范围
const formatDateRange = (dateRange) => {
  if (!dateRange || dateRange.length !== 2) {
    const today = getTodayDateString();
    return { dateBegin: today, dateEnd: today };
  }

  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return {
    dateBegin: formatDate(dateRange[0]),
    dateEnd: formatDate(dateRange[1])
  };
};

// 获取商家信息
const loadMerchantInfo = async () => {
  try {
    await userStore.getMerchantInfo();
    console.log("商家信息加载成功:", merchantInfo.value);
  } catch (error) {
    console.error("获取商家信息失败:", error);
    ElMessage.error("获取商家信息失败");
  }
};

// 获取统计数据
const loadStatisticData = async (dateBegin, dateEnd) => {
  try {
    const response = await getStatistic(dateBegin, dateEnd);
    if (response.code === 200) {
      statisticData.value = response.data || [];
      console.log("统计数据加载成功:", statisticData.value);
      return statisticData.value;
    } else {
      console.error("获取统计数据失败:", response.message);
      ElMessage.error(response.message || "获取统计数据失败");
      return [];
    }
  } catch (error) {
    console.error("获取统计数据失败:", error);
    ElMessage.error("获取统计数据失败");
    return [];
  }
};

// 获取统计汇总数据
const loadStatisticSumData = async () => {
  try {
    const response = await getStatisticSum();
    if (response.code === 200) {
      statisticSumData.value = response.data || {};
      console.log("统计汇总数据加载成功:", statisticSumData.value);
    } else {
      console.error("获取统计汇总数据失败:", response.message);
      ElMessage.error(response.message || "获取统计汇总数据失败");
    }
  } catch (error) {
    console.error("获取统计汇总数据失败:", error);
    ElMessage.error("获取统计汇总数据失败");
  }
};

// --- 商家信息编辑弹窗 ---
const editDialogVisible = ref(false);

// --- 修改密码弹窗 ---
const passwordDialogVisible = ref(false);
const passwordFormRef = ref(null); // 表单引用
const smsCountdown = ref(0); // 短信验证码倒计时
const smsTimer = ref(null); // 倒计时定时器
const smsLoading = ref(false); // 发送验证码loading状态
const passwordForm = reactive({
  phone: "",
  password: "",
  confirmPassword: "",
  smsCode: "",
});

// 校验新密码和确认密码是否一致
const validatePass2 = (_, value, callback) => {
  if (value === "") {
    callback(new Error("请再次输入新密码"));
  } else if (value !== passwordForm.password) {
    callback(new Error("两次输入密码不一致!"));
  } else {
    callback();
  }
};

const passwordRules = reactive({
  phone: [
    { required: true, message: "手机号不能为空", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "新密码不能为空", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "确认密码不能为空", trigger: "blur" },
    { validator: validatePass2, trigger: "blur" },
  ],
  smsCode: [
    { required: true, message: "验证码不能为空", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "请输入6位数字验证码", trigger: "blur" },
  ],
});

// 开始倒计时
const startCountdown = () => {
  smsCountdown.value = 60;
  smsTimer.value = setInterval(() => {
    smsCountdown.value--;
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer.value);
      smsTimer.value = null;
    }
  }, 1000);
};

// 清除倒计时
const clearCountdown = () => {
  if (smsTimer.value) {
    clearInterval(smsTimer.value);
    smsTimer.value = null;
  }
  smsCountdown.value = 0;
};

// 处理手机号输入，只允许数字
const handlePhoneInput = (value) => {
  // 只保留数字
  passwordForm.phone = value.replace(/\D/g, "");
};

// 发送短信验证码
const handleSendSmsCode = async () => {
  // 如果正在倒计时中，不允许重复发送
  if (smsCountdown.value > 0) {
    ElMessage.warning(`请等待 ${smsCountdown.value} 秒后再试`);
    return;
  }

  if (!passwordForm.phone) {
    ElMessage.warning("请先输入手机号");
    return;
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(passwordForm.phone)) {
    ElMessage.warning("请输入正确的手机号");
    return;
  }

  smsLoading.value = true;

  try {
    // 调用发送短信接口，scene=2表示重置密码
    const response = await sendSmsCode(passwordForm.phone, 2);

    if (response.code === 200) {
      ElMessage.success("验证码发送成功，请查收短信");
      // 开始60秒倒计时
      startCountdown();
    } else {
      ElMessage.error(response.message || "验证码发送失败");
    }
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error("发送验证码失败，请稍后重试");
  } finally {
    smsLoading.value = false;
  }
};

// 打开编辑商家信息弹窗
const openEditDialog = () => {
  editDialogVisible.value = true;
};

// 处理编辑成功
const handleEditSuccess = () => {
  // 重新获取商家信息
  loadMerchantInfo();
};

// 打开修改密码弹窗
const openPasswordDialog = () => {
  passwordDialogVisible.value = true;
};

// 重置表单
const resetPasswordForm = () => {
  passwordFormRef.value?.resetFields(); // 使用 optional chaining
  clearCountdown(); // 清除倒计时
};

// 提交密码修改
const handlePasswordSubmit = () => {
  passwordFormRef.value?.validate(async (valid) => {
    // 使用 optional chaining
    if (valid) {
      try {
        const requestData = {
          phone: passwordForm.phone,
          password: passwordForm.password,
          smsCode: passwordForm.smsCode,
        };

        console.log("提交修改密码请求:", requestData);
        const response = await changePassword(requestData);

        if (response.code === 200) {
          ElMessage.success("密码修改成功");
          passwordDialogVisible.value = false; // 关闭弹窗
          resetPasswordForm(); // 重置表单
        } else {
          ElMessage.error(response.message || "密码修改失败");
        }
      } catch (error) {
        console.error("修改密码失败:", error);
        ElMessage.error("密码修改失败，请稍后重试");
      }
    } else {
      console.log("密码表单校验失败");
      return false;
    }
  });
};

// --- ECharts ---
const incomeChart = ref(null);
const browseChart = ref(null);
let incomeChartInstance = null;
let browseChartInstance = null;

// 创建收入图表配置
const createIncomeChartOption = (data) => {
  // 检查是否有数据
  if (!data || data.length === 0) {
    return {
      title: {
        text: '📊 暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 16
        }
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: [],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false }
      },
      yAxis: {
        type: "value",
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { show: false }
      },
      grid: { left: "3%", right: "4%", bottom: "3%", containLabel: true },
      series: []
    };
  }

  const xAxisData = data.map(item => item.dayTime);
  const seriesData = data.map(item => item.inComeAmount || 0);

  return {
    tooltip: {
      trigger: "axis",
      formatter: "{b}<br/>收入：{c} 元"
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xAxisData,
    },
    yAxis: {
      type: "value",
      axisLabel: { formatter: "{value} 元" }
    },
    grid: { left: "3%", right: "4%", bottom: "3%", containLabel: true },
    series: [
      {
        name: "收入",
        type: "line",
        smooth: true,
        data: seriesData,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(64, 158, 255, 0.3)" },
            { offset: 1, color: "rgba(64, 158, 255, 0)" },
          ]),
        },
        itemStyle: { color: "#409EFF" },
        emphasis: { focus: "series" },
        tooltip: { valueFormatter: (value) => value + " 元" },
      },
    ],
  };
};

// 创建浏览量图表配置
const createBrowseChartOption = (data) => {
  // 检查是否有数据
  if (!data || data.length === 0) {
    return {
      title: {
        text: '📈 暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 16
        }
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: [],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false }
      },
      yAxis: {
        type: "value",
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { show: false }
      },
      grid: { left: "3%", right: "4%", bottom: "3%", containLabel: true },
      series: []
    };
  }

  const xAxisData = data.map(item => item.dayTime);
  const seriesData = data.map(item => item.shopBrowsCount || 0);

  return {
    tooltip: {
      trigger: "axis",
      formatter: "{b}<br/>浏览量：{c} 次"
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xAxisData,
    },
    yAxis: {
      type: "value",
      axisLabel: { formatter: "{value} 次" }
    },
    grid: { left: "3%", right: "4%", bottom: "3%", containLabel: true },
    series: [
      {
        name: "浏览量",
        type: "line",
        smooth: true,
        data: seriesData,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(103, 194, 58, 0.3)" },
            { offset: 1, color: "rgba(103, 194, 58, 0)" },
          ]),
        },
        itemStyle: { color: "#67C23A" },
        emphasis: { focus: "series" },
        tooltip: { valueFormatter: (value) => value + " 次" },
      },
    ],
  };
};

// 初始化图表
const initCharts = async () => {
  // 获取今日统计数据
  const today = getTodayDateString();
  const data = await loadStatisticData(today, today);

  if (incomeChart.value && !incomeChartInstance) {
    incomeChartInstance = echarts.init(incomeChart.value);
    incomeChartInstance.setOption(createIncomeChartOption(data));
  }
  if (browseChart.value && !browseChartInstance) {
    browseChartInstance = echarts.init(browseChart.value);
    browseChartInstance.setOption(createBrowseChartOption(data));
  }
};

// 更新收入图表
const updateIncomeChart = async (dateRange) => {
  const { dateBegin, dateEnd } = formatDateRange(dateRange);
  const data = await loadStatisticData(dateBegin, dateEnd);

  if (incomeChartInstance) {
    incomeChartInstance.setOption(createIncomeChartOption(data));
  }
};

// 更新浏览量图表
const updateBrowseChart = async (dateRange) => {
  const { dateBegin, dateEnd } = formatDateRange(dateRange);
  const data = await loadStatisticData(dateBegin, dateEnd);

  if (browseChartInstance) {
    browseChartInstance.setOption(createBrowseChartOption(data));
  }
};

const destroyCharts = () => {
  if (incomeChartInstance) {
    incomeChartInstance.dispose();
    incomeChartInstance = null;
  }
  if (browseChartInstance) {
    browseChartInstance.dispose();
    browseChartInstance = null;
  }
};

const resizeCharts = () => {
  if (incomeChartInstance) {
    incomeChartInstance.resize();
  }
  if (browseChartInstance) {
    browseChartInstance.resize();
  }
};

// 监听日期范围变化
watch(dateRange1, (newVal) => {
  if (newVal && newVal.length === 2) {
    updateIncomeChart(newVal);
  }
}, { immediate: false }); // 不立即执行，因为初始化时会调用

watch(dateRange2, (newVal) => {
  if (newVal && newVal.length === 2) {
    updateBrowseChart(newVal);
  }
}, { immediate: false }); // 不立即执行，因为初始化时会调用

onMounted(() => {
  nextTick(() => {
    initCharts();
  });
  window.addEventListener("resize", resizeCharts);
  // 加载商家信息
  loadMerchantInfo();
  // 加载统计汇总数据
  loadStatisticSumData();
});

onBeforeUnmount(() => {
  destroyCharts();
  window.removeEventListener("resize", resizeCharts);
  clearCountdown(); // 清除短信验证码倒计时
});
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;
  height: calc(100vh - 86px);
  overflow-y: auto;
}

.box-card {
  margin-bottom: 20px;
  border: none;
  :deep(.el-card__header) {
    border-bottom: 1px solid #e4e7ed;
    padding: 15px 20px;
  }
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.merchant-info {
  :deep(.el-card__header) {
    border-bottom: none;
  }
  .merchant-logo-col {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .merchant-logo-placeholder {
    width: 80px;
    height: 80px;
    background-color: #f5f7fa;
    color: #c0c4cc;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    font-size: 14px;
  }
  .merchant-logo {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    cursor: pointer;
  }
  .merchant-details {
    .merchant-name {
      font-size: 18px;
      font-weight: bold;
      margin-top: 0;
      margin-bottom: 10px;
    }
    .merchant-descriptions {
      :deep(.el-descriptions__label.info-label) {
        width: 70px;
        text-align: right !important;
        color: #606266;
        font-weight: normal;
        padding-right: 5px !important;
      }
      :deep(.el-descriptions__content) {
        word-break: break-all;
        color: #303133;
      }
      border: none;
      :deep(td),
      :deep(th) {
        border: none !important;
        padding-top: 4px !important;
        padding-bottom: 4px !important;
      }
    }
  }
  .merchant-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }
  .merchant-images {
    margin-top: 10px;
  }
  .image-placeholder {
    background-color: #f5f7fa;
    color: #c0c4cc;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70px;
    border-radius: 4px;
    font-size: 12px;
  }
  .merchant-image {
    width: 100%;
    height: 70px;
    border-radius: 4px;
    cursor: pointer;
  }
}

.data-stats {
  margin-bottom: 20px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 15px;

    @media (max-width: 1400px) {
      grid-template-columns: repeat(4, 1fr);
      .stat-card:nth-child(n + 5) {
        grid-column: span 1;
      }
    }

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
      .stat-card:nth-child(n + 4) {
        grid-column: span 1;
      }
    }

    @media (max-width: 992px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }
  }

  .stat-card {
    text-align: center;
    border: none;
    background-color: #fff;
    border-radius: 4px;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    :deep(.el-card__body) {
      padding: 12px 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
    }

    .stat-title {
      color: #909399;
      font-size: 12px;
      margin-bottom: 6px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }

    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #f56c6c;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }
  }
}

.chart-section {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
  }
  .chart-container {
    height: 300px;
    width: 100%;
  }
}

// 响应式调整
@media (max-width: 1400px) {
  .data-stats .stat-card {
    .stat-title {
      font-size: 11px;
    }
    .stat-value {
      font-size: 18px;
    }
  }
}

@media (max-width: 1200px) {
  .data-stats .stat-card {
    .stat-title {
      font-size: 11px;
    }
    .stat-value {
      font-size: 16px;
    }
  }
}

@media (max-width: 992px) {
  .data-stats .stat-card {
    .stat-title {
      font-size: 11px;
    }
    .stat-value {
      font-size: 16px;
    }
  }
}

@media (max-width: 768px) {
  .merchant-info {
    .merchant-logo-col {
      margin-bottom: 15px;
    }
    .merchant-actions {
      justify-content: flex-start;
      margin-top: 15px;
    }
    .merchant-images {
      margin-top: 10px;
    }
  }

  .data-stats .stat-card {
    min-height: 80px;
    .stat-title {
      font-size: 11px;
      margin-bottom: 6px;
    }
    .stat-value {
      font-size: 16px;
    }
  }

  .chart-section .card-header {
    flex-direction: column;
    align-items: flex-start;
    .el-date-picker {
      margin-top: 10px;
      width: 100% !important;
    }
  }
}

@media (max-width: 480px) {
  .data-stats .stat-card {
    min-height: 70px;
    .stat-title {
      font-size: 10px;
      margin-bottom: 4px;
    }
    .stat-value {
      font-size: 14px;
    }
  }
}
</style>
