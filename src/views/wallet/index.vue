<template>
  <div class="app-container">
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" lazy class="wallet-tabs">
      <el-tab-pane label="充值记录" name="recharge">
        <recharge-record ref="rechargeRecordRef" />
      </el-tab-pane>
      <el-tab-pane label="提现信息管理" name="withdrawInfo">
        <withdraw-info-management ref="withdrawInfoRef" />
      </el-tab-pane>
      <el-tab-pane label="提现记录" name="withdraw">
        <withdraw-record ref="withdrawRecordRef" />
      </el-tab-pane>
      <el-tab-pane label="收入记录" name="income">
        <income-record ref="incomeRecordRef" />
      </el-tab-pane>
      <el-tab-pane label="消费记录" name="expense">
        <expense-record ref="expenseRecordRef" />
      </el-tab-pane>
      <el-tab-pane label="退款记录" name="refund">
        <refund-record ref="refundRecordRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Wallet">
import { ref } from 'vue';
import RechargeRecord from './components/RechargeRecord.vue';
import WithdrawInfoManagement from './components/WithdrawInfoManagement.vue';
import WithdrawRecord from './components/WithdrawRecord.vue';
import IncomeRecord from './components/IncomeRecord.vue';
import ExpenseRecord from './components/ExpenseRecord.vue';
import RefundRecord from './components/RefundRecord.vue';

// 当前激活的标签页
const activeTab = ref('recharge');

// 子组件引用
const rechargeRecordRef = ref(null);
const withdrawInfoRef = ref(null);
const withdrawRecordRef = ref(null);
const incomeRecordRef = ref(null);
const expenseRecordRef = ref(null);
const refundRecordRef = ref(null);
</script>

<style lang="scss" scoped>
.wallet-tabs {
  margin-top: 10px;
  
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
  
  :deep(.el-tabs__item) {
    font-size: 15px;
    padding: 0 20px;
  }
}
</style>