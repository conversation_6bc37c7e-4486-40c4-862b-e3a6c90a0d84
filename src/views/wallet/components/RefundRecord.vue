<template>
  <div class="refund-record">
    <!-- 退款记录表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="refundList" style="width: 100%" border v-loading="loading">
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="orderNo" label="订单号" align="center" />
        <el-table-column prop="waterName" label="流水名称" align="center" />
        <el-table-column prop="money" label="退款金额" align="center">
          <template #default="scope">
            <span style="color: #E6A23C;">+{{ scope.row.money }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="showContent" label="显示内容" align="center" />
        <el-table-column prop="createdAt" label="退款时间" align="center" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import { listWaterRecord } from '@/api/water';

// 加载状态
const loading = ref(false);
// 退款记录列表
const refundList = ref([]);
// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取退款记录数据
const getRefundList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      waterType: [9] // 9表示退款
    };

    const response = await listWaterRecord(params);
    if (response.code === 200) {
      refundList.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取退款记录失败');
    }
  } catch (error) {
    console.error('获取退款记录失败:', error);
    ElMessage.error('获取退款记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理每页显示数量变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  getRefundList();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getRefundList();
};

// 组件挂载时获取数据
onMounted(() => {
  getRefundList();
});

// 使用keep-alive时重新激活获取新数据
onActivated(() => {
  getRefundList();
});
</script>

<style lang="scss" scoped>
.refund-record {
  .table-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>