<template>
  <div class="income-record">
    <!-- 收入记录表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="incomeList" style="width: 100%" border v-loading="loading">
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="orderNo" label="订单号" align="center" />
        <el-table-column prop="waterName" label="流水名称" align="center" />
        <el-table-column prop="money" label="金额" align="center">
          <template #default="scope">
            <span style="color: #67C23A;">+{{ scope.row.money }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="showContent" label="显示内容" align="center" />
        <el-table-column prop="createdAt" label="创建时间" align="center" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import { listWaterRecord } from '@/api/water';

// 加载状态
const loading = ref(false);
// 收入记录列表
const incomeList = ref([]);
// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取收入记录数据
const getIncomeList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      waterType: [8] // 8表示收入
    };

    const response = await listWaterRecord(params);
    if (response.code === 200) {
      incomeList.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取收入记录失败');
    }
  } catch (error) {
    console.error('获取收入记录失败:', error);
    ElMessage.error('获取收入记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理每页显示数量变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  getIncomeList();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getIncomeList();
};

// 组件挂载时获取数据
onMounted(() => {
  getIncomeList();
});

// 使用keep-alive时重新激活获取新数据
onActivated(() => {
  getIncomeList();
});
</script>

<style lang="scss" scoped>
.income-record {
  .table-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>