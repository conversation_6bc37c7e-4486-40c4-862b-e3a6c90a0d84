<template>
  <div class="recharge-record-container">
    <!-- 账户余额和充值按钮 -->
    <el-card class="balance-card" shadow="never">
      <div class="balance-info">
        <div class="balance-item">
          <div class="balance-label">账户余额</div>
          <div class="balance-amount">{{ formatAmount(purseInfo.balance) }}</div>
        </div>
        <div class="balance-item">
          <div class="balance-label">冻结余额</div>
          <div class="balance-amount frozen">{{ formatAmount(purseInfo.frozenAmount) }}</div>
        </div>
      </div>
      <el-button type="primary" @click="openRechargeDialog">充值</el-button>
    </el-card>

    <!-- 充值记录表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="rechargeList" style="width: 100%" border v-loading="loading">
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="amount" label="充值金额" align="center">
          <template #default="scope">
            {{ formatAmount(scope.row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="充值时间" align="center" />
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 充值弹窗 -->
    <el-dialog
      title="充值"
      v-model="rechargeDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="recharge-dialog-content">
        <div class="warning-message">
          <el-icon><warning /></el-icon>
          <span>使用模拟扫码二维码进行充值，充值成功后点击确认进行刷新</span>
        </div>
        <div class="recharge-amount">
          <div class="amount-label">充值金额</div>
          <div class="amount-value">{{ rechargeAmount }}</div>
        </div>
        <div class="qrcode-placeholder"></div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rechargeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRecharge">确认充值</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import { Warning } from '@element-plus/icons-vue';
import { getPurseInfo, getRechargeRecordList } from '@/api/wallet';

// 钱包信息
const purseInfo = reactive({
  balance: 0,
  frozenAmount: 0
});

// 充值金额
const rechargeAmount = ref('300.00');

// 充值弹窗可见性
const rechargeDialogVisible = ref(false);

// 加载状态
const loading = ref(false);

// 充值记录列表
const rechargeList = ref([]);

// 分页信息
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
});

// 打开充值弹窗
const openRechargeDialog = () => {
  return ElMessage.warning('充值功能开发中～');
  // rechargeDialogVisible.value = true;
};

// 确认充值
const confirmRecharge = () => {
  ElMessage.success('充值成功');
  rechargeDialogVisible.value = false;
  // 刷新钱包信息和充值记录
  fetchPurseInfo();
  fetchRechargeList();
};

// 获取钱包信息
const fetchPurseInfo = async () => {
  try {
    const response = await getPurseInfo();
    if (response.code === 200) {
      purseInfo.balance = response.data.balance || 0;
      purseInfo.frozenAmount = response.data.frozenAmount || 0;
    }
  } catch (error) {
    console.error('获取钱包信息失败:', error);
    ElMessage.error('获取钱包信息失败');
  }
};

// 获取充值记录列表
const fetchRechargeList = async () => {
  loading.value = true;
  try {
    const response = await getRechargeRecordList(queryParams);
    if (response.code === 200) {
      rechargeList.value = response.data.list || [];
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取充值记录失败:', error);
    ElMessage.error('获取充值记录失败');
  } finally {
    loading.value = false;
  }
};

// 分页大小改变
const handleSizeChange = (val) => {
  queryParams.pageSize = val;
  queryParams.pageNum = 1;
  fetchRechargeList();
};

// 当前页改变
const handleCurrentChange = (val) => {
  queryParams.pageNum = val;
  fetchRechargeList();
};

// 格式化金额显示
const formatAmount = (amount) => {
  return (amount || 0).toFixed(2);
};

// 获取状态对应的类型
const getStatusType = (status) => {
  switch (status) {
    case '成功':
      return 'success';
    case '失败':
      return 'danger';
    default:
      return 'info';
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPurseInfo();
  fetchRechargeList();
});

// 组件激活时获取数据（用于 keep-alive 缓存的组件）
onActivated(() => {
  fetchPurseInfo();
  fetchRechargeList();
});
</script>

<style lang="scss" scoped>
.recharge-record-container {
  .balance-card {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    :deep(.el-card__body) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .balance-info {
      display: flex;
      gap: 40px;
      align-items: baseline;

      .balance-item {
        display: flex;
        align-items: baseline;

        .balance-label {
          font-size: 14px;
          color: #606266;
          margin-right: 10px;
        }

        .balance-amount {
          font-size: 24px;
          font-weight: bold;
          color: #f56c6c;

          &.frozen {
            color: #909399;
          }
        }
      }
    }
  }

  .table-card {
    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0 20px;
    }

    :deep(.el-tabs__content) {
      padding: 20px;
    }

    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: end;
    }
  }

  .recharge-dialog-content {
    .warning-message {
      display: flex;
      align-items: center;
      color: #e6a23c;
      margin-bottom: 20px;
      font-size: 14px;

      .el-icon {
        margin-right: 8px;
      }
    }

    .recharge-amount {
      text-align: center;
      margin-bottom: 20px;

      .amount-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 5px;
      }

      .amount-value {
        font-size: 28px;
        font-weight: bold;
        color: #f56c6c;
      }
    }

    .qrcode-placeholder {
      width: 200px;
      height: 200px;
      background-color: #f5f7fa;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
      font-size: 14px;
    }
  }
}
</style>