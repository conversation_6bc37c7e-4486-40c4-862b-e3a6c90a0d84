<template>
  <div class="expense-record">
    <!-- 消费记录表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="expenseList" style="width: 100%" border v-loading="loading">
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="orderNo" label="订单号" align="center" />
        <el-table-column prop="waterName" label="流水名称" align="center" />
        <el-table-column prop="money" label="消费金额" align="center">
          <template #default="scope">
            <span style="color: #F56C6C;">-{{ scope.row.money }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="showContent" label="显示内容" align="center" />
        <el-table-column prop="createdAt" label="消费时间" align="center" />
        <el-table-column prop="status" label="开票状态" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 0"
              type="primary"
              link
              @click="handleInvoice(scope.row)"
              :loading="scope.row.invoicing"
            >
              开具发票
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>


  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listWaterRecord } from '@/api/water';
import { saveInvoice } from '@/api/invoice';

// 加载状态
const loading = ref(false);
// 消费记录列表
const expenseList = ref([]);
// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取消费记录数据
const getExpenseList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      waterType: [10] // 10表示消费
    };

    const response = await listWaterRecord(params);
    if (response.code === 200) {
      expenseList.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取消费记录失败');
    }
  } catch (error) {
    console.error('获取消费记录失败:', error);
    ElMessage.error('获取消费记录失败');
  } finally {
    loading.value = false;
  }
};



// 获取开票状态对应的类型
const getStatusType = (status) => {
  switch (status) {
    case 0:
      return 'info';
    case 1:
      return 'warning';
    case 2:
      return 'success';
    default:
      return 'info';
  }
};

// 获取开票状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0:
      return '未开票';
    case 1:
      return '开票中';
    case 2:
      return '已开票';
    default:
      return '未开票';
  }
};

// 处理每页显示数量变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  getExpenseList();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getExpenseList();
};

// 处理开票
const handleInvoice = async (row) => {
  try {
    await ElMessageBox.confirm('确认要开具发票吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 设置当前行的开票状态为加载中
    row.invoicing = true;

    const invoiceData = {
      type: 1, // 发票类型 1.支出
      recordId: row.id // 支出记录ID
    };

    const response = await saveInvoice(invoiceData);

    if (response.code === 200) {
      // 更新状态为已开票
      row.status = 2;
      ElMessage.success('开票成功');
    } else {
      ElMessage.error(response.message || '开票失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开票失败:', error);
      ElMessage.error('开票失败，请稍后重试');
    }
  } finally {
    // 清除加载状态
    row.invoicing = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  getExpenseList();
});

// 使用keep-alive时重新激活获取新数据
onActivated(() => {
  getExpenseList();
});
</script>

<style lang="scss" scoped>
.expense-record {
  .table-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>