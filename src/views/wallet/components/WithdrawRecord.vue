<template>
  <div class="withdraw-record">
    <!-- 提现记录表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="withdrawRecordList" style="width: 100%" border v-loading="loading">
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="orderNo" label="订单号" align="center" />
        <el-table-column prop="nickname" label="提现人昵称" align="center" />
        <el-table-column prop="wx" label="提现人微信号" align="center" />
        <el-table-column prop="phone" label="提现人手机号" align="center" />
        <el-table-column prop="amount" label="提现金额" align="center">
          <template #default="scope">
            ¥{{ scope.row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="payoutTime" label="提现时间" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="失败原因" align="center">
          <template #default="scope">
            <span v-if="scope.row.status === '3'">{{ scope.row.reason || '-' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import { getWithdrawRecordList } from '@/api/wallet';

// 提现记录列表
const withdrawRecordList = ref([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 加载状态
const loading = ref(false);

// 获取状态对应的类型
const getStatusType = (status) => {
  switch (status) {
    case '2': // 已提现
      return 'success';
    case '3': // 提现失败
      return 'danger';
    case '1': // 提现中
      return 'warning';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case '1':
      return '提现中';
    case '2':
      return '已提现';
    case '3':
      return '提现失败';
    default:
      return '未知状态';
  }
};

// 获取提现记录列表
const fetchWithdrawRecordList = async () => {
  loading.value = true;
  try {
    const response = await getWithdrawRecordList({
      pageNum: currentPage.value,
      pageSize: pageSize.value
    });
    if (response.code === 200) {
      withdrawRecordList.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取提现记录失败');
    }
  } catch (error) {
    console.error('获取提现记录失败:', error);
    ElMessage.error('获取提现记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理每页显示数量变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
  fetchWithdrawRecordList();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchWithdrawRecordList();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchWithdrawRecordList();
});

// 组件激活时获取数据（用于 keep-alive 缓存的组件）
onActivated(() => {
  fetchWithdrawRecordList();
});
</script>

<style lang="scss" scoped>
.withdraw-record {
  .table-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>