<template>
  <div class="withdraw-info-management">
    <!-- 提现信息管理表格 -->
    <el-card shadow="never" class="table-card">
      <el-table :data="withdrawInfoList" style="width: 100%" border>
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="name" label="提现人姓名" align="center" />
        <el-table-column prop="wechatId" label="提现人微信号" align="center" />
        <el-table-column prop="phone" label="提现人手机号" align="center" />
        <el-table-column prop="amount" label="提现金额" align="center" />
        <el-table-column prop="verifyCode" label="手机验证码" align="center" />
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 提现信息查看/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="dialog-content">
        <div class="info-display" v-if="viewMode">
          <div class="info-item">
            <span class="label">提现人姓名：</span>
            <span class="value">{{ currentInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">提现人手机号：</span>
            <span class="value">{{ currentInfo.phone }}</span>
          </div>
          <div class="info-item">
            <span class="label">提现人微信号：</span>
            <span class="value">{{ currentInfo.wechatId }}</span>
          </div>
          <div class="info-item">
            <span class="label">验证码：</span>
            <span class="value">{{ currentInfo.verifyCode }}</span>
          </div>
        </div>
        <el-form
          v-else
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="edit-form"
        >
          <el-form-item label="提现人姓名" prop="name">
            <el-input v-model="form.name" placeholder="请选择" />
          </el-form-item>
          <el-form-item label="提现人手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="0" />
          </el-form-item>
          <el-form-item label="提现人微信号" prop="wechatId">
            <el-input v-model="form.wechatId" placeholder="0" />
          </el-form-item>
          <el-form-item label="验证码" prop="verifyCode">
            <el-input v-model="form.verifyCode" placeholder="0" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button v-if="!viewMode" type="primary" @click="handleSubmit">确认发布</el-button>
          <el-button v-else type="primary" @click="switchToEdit">编辑</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';

// 提现信息列表
const withdrawInfoList = ref([
  {
    id: 1,
    name: '奥尔罕',
    wechatId: 'wx_2364312',
    phone: '13245678910',
    amount: '30.00',
    verifyCode: '628741'
  },
  {
    id: 2,
    name: '代码费',
    wechatId: 'wx_2364312',
    phone: '13245678910',
    amount: '100.00',
    verifyCode: '748520'
  }
]);

// 弹窗相关
const dialogVisible = ref(false);
const viewMode = ref(true);
const dialogTitle = computed(() => viewMode.value ? '提现信息' : '编辑提现信息');

// 当前操作的信息
const currentInfo = ref({});

// 表单相关
const formRef = ref(null);
const form = reactive({
  name: '',
  phone: '',
  wechatId: '',
  verifyCode: ''
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入提现人姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入提现人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  wechatId: [{ required: true, message: '请输入提现人微信号', trigger: 'blur' }],
  verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
};

// 查看详情
const handleEdit = (row) => {
  currentInfo.value = { ...row };
  viewMode.value = true;
  dialogVisible.value = true;
};

// 切换到编辑模式
const switchToEdit = () => {
  viewMode.value = false;
  Object.assign(form, currentInfo.value);
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 更新数据
      const index = withdrawInfoList.value.findIndex(item => item.id === currentInfo.value.id);
      if (index !== -1) {
        withdrawInfoList.value[index] = { ...withdrawInfoList.value[index], ...form };
      }
      ElMessage.success('保存成功');
      dialogVisible.value = false;
    } else {
      return false;
    }
  });
};
</script>

<style lang="scss" scoped>
.withdraw-info-management {
  .table-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .dialog-content {
    .info-display {
      .info-item {
        margin-bottom: 15px;
        display: flex;
        
        .label {
          width: 120px;
          color: #606266;
          text-align: right;
          padding-right: 12px;
        }
        
        .value {
          flex: 1;
          color: #303133;
        }
      }
    }
    
    .edit-form {
      max-width: 400px;
      margin: 0 auto;
    }
  }
}
</style>