<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
         <el-form-item label="文章标题" prop="title">
            <el-input
               v-model="queryParams.title"
               placeholder="请输入文章标题"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['cms:article:add']"
            >发布文章</el-button>
         </el-col>
      </el-row>

    <el-table v-loading="loading" :data="articleList">
      <el-table-column label="封面" align="center" prop="cover" width="100">
         <template #default="scope">
            <!-- 封面图片展示 -->
            <el-image style="width: 60px; height: 60px" :src="scope.row.coverImage || defaultCover" fit="cover"></el-image>
         </template>
      </el-table-column>
      <el-table-column label="文章标题" align="left" prop="title" :show-overflow-tooltip="true">
        <template #default="scope">
          <!-- 点击标题进行预览 -->
          <el-link type="primary" @click="handlePreview(scope.row)">{{ scope.row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishedAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cms:article:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cms:article:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 文章表单弹窗 -->
    <article-form
      v-model="dialogVisible"
      :article-data="currentArticle"
      :is-edit="isEditMode"
      :is-preview="isPreviewMode"
      @submitSuccess="getList"
    />
  </div>
</template>

<script setup name="Article">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import ArticleForm from './ArticleForm.vue'; // 引入表单组件
import { listPost, deletePost } from "@/api/post";
import defaultCover from '@/assets/images/profile.jpg'; // 假设的默认封面图路径, 需要确认或替换

const { proxy } = getCurrentInstance();
const { parseTime } = proxy; // 假设 parseTime 在全局挂载

// 加载状态
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 文章表格数据
const articleList = ref([]);
// 弹窗相关状态
const dialogVisible = ref(false); // 控制弹窗显示
const isEditMode = ref(false); // 是否为编辑模式
const isPreviewMode = ref(false); // 新增：是否为预览模式
const currentArticle = ref({}); // 当前操作（编辑或预览）的文章数据

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  // 其他查询参数...
});

// Form Ref
const queryRef = ref(null); // Add ref for the query form

/** 查询文章列表 */
function getList() {
  if(loading.value) return
  loading.value = true;
  listPost(queryParams).then(response => {
    articleList.value = response.data.list;
    total.value = response.data.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  if (queryRef.value) {
      queryRef.value.resetFields(); // Use the ref to reset form
  }
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  isEditMode.value = false;
  isPreviewMode.value = false; // 确保不是预览模式
  currentArticle.value = {}; // 清空当前文章数据
  dialogVisible.value = true; // 打开弹窗
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const articleId = row.id;
  // 实际应调用API: getArticle(articleId).then(response => {
  //   currentArticle.value = response.data;
  //   isEditMode.value = true;
  //   isPreviewMode.value = false; // 确保不是预览模式
  //   dialogVisible.value = true;
  // });
  // 模拟获取数据
  currentArticle.value = { ...row }; // 填充当前文章数据
  isEditMode.value = true;
  isPreviewMode.value = false; // 确保不是预览模式
  dialogVisible.value = true; // 打开弹窗
}

/** 预览按钮操作 */
function handlePreview(row) {
  const articleId = row.id;
  // 实际应调用API: getArticle(articleId).then(response => {
  //   currentArticle.value = response.data;
  //   isEditMode.value = false; // 不是编辑模式
  //   isPreviewMode.value = true; // 设置为预览模式
  //   dialogVisible.value = true;
  // });
  // 模拟获取数据
  currentArticle.value = { ...row }; // 填充当前文章数据
  isEditMode.value = false; // 不是编辑模式
  isPreviewMode.value = true; // 设置为预览模式
  dialogVisible.value = true; // 打开弹窗
}

/** 删除按钮操作 */
function handleDelete(row) {
  const articleId = row.id;
  proxy.$modal.confirm('是否确认删除文章标题为"' + row.title + '"的数据项？').then(function() {
    return deletePost({ postId: articleId });
  }).then(() => {
    getList(); // 重新加载列表
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 (如果需要) */
// function handleExport() {
//    proxy.download('cms/article/export', { // 确认导出API路径
//      ...queryParams
//    }, `article_${new Date().getTime()}.xlsx`)
//    proxy.$modal.msgWarning("导出功能待实现");
// }

onMounted(() => {
  getList(); // 组件挂载后获取列表数据
});

// keep-alive 组件激活时重新获取数据
onActivated(() => {
  getList(); // 每次进入页面都获取最新数据
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 10px;
}
.el-table {
  height: calc(100vh - 270px) !important;
}
</style>