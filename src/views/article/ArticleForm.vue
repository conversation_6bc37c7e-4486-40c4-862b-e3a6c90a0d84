<template>
  <el-dialog
    :title="title"
    v-model="visible"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    @close="handleClose"
    custom-class="article-dialog"
    :fullscreen="isMobile"
  >
    <!-- 双重标题 -->
    <template #title>
      <div class="dialog-title">
        <span v-if="isPreview">文章详情</span>
        <span v-else-if="isEdit">编辑文章</span>
        <span v-else>发布文章</span>
      </div>
    </template>

    <el-alert v-if="!isPreview"
      title="注意事项：平台内不得发布任何关于欺诈、赌博、色情、违法等内容。"
      type="warning"
      show-icon
      :closable="false"
      class="mb10"
    />

    <el-form
      ref="articleFormRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      label-position="top"
      :disabled="isPreview"  
    >
      <el-row :gutter="isMobile ? 0 : 20">
        <!-- 左侧表单项 -->
        <el-col :span="isMobile ? 24 : 10">
          <el-form-item label="封面图" prop="cover">
             <ImageUpload
                v-model="form.cover"
                :limit="1"
                class="cover-uploader"
                :disabled="isPreview"
              >
                <!-- Tip slot -->
                 <template #tip v-if="!isPreview">
                    <div class="el-upload__tip">
                      只能上传一张封面图, 建议尺寸 XXX*XXX
                    </div>
                  </template>
              </ImageUpload>
          </el-form-item>

          <el-form-item :label="`图片 (${form.images ? form.images.split(',').filter(url => url).length : 0}/3)`" prop="images">
             <ImageUpload
                v-model="form.images"
                multiple
                :limit="3"
                class="image-uploader"
                :disabled="isPreview"
                :needAddSize="true"
              >
               <!-- Tip slot -->
                <template #tip v-if="!isPreview">
                    <div class="el-upload__tip">
                      最多上传3张图片
                    </div>
                  </template>
              </ImageUpload>
          </el-form-item>


        </el-col>

        <!-- 右侧表单项 -->
        <el-col :span="isMobile ? 24 : 14">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <!-- 考虑替换为富文本编辑器 -->
            <el-input
              type="textarea"
              :rows="isMobile ? 10 : 18"
              v-model="form.content"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="!isPreview" @click="cancel">取 消</el-button>
        <el-button v-if="!isPreview" type="primary" @click="submitForm">{{ isEdit ? '确认修改' : '确认发布' }}</el-button>
        <el-button v-else type="primary" @click="cancel">关 闭</el-button> 
      </div>
    </template>

    <!-- 图片预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="600px"
      append-to-body
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  computed, // 引入 computed
  onMounted,
  onUnmounted
} from "vue";
import ImageUpload from "@/components/ImageUpload/index.vue";
import { publishPost, updatePost } from "@/api/post";
// import type { UploadProps, UploadUserFile, UploadFile, UploadFiles } from 'element-plus'; // 按需引入类型

const props = defineProps({
  modelValue: {
    // 控制弹窗显示/隐藏
    type: Boolean,
    required: true,
  },
  articleData: {
    // 编辑时传入的文章数据
    type: Object,
    default: () => ({}),
  },
  isEdit: {
    // 是否为编辑状态
    type: Boolean,
    default: false,
  },
  isPreview: { // 新增：是否为预览模式
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(["update:modelValue", "submitSuccess"]);

const { proxy } = getCurrentInstance(); // 获取全局方法

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '900px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '5vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// --- 上传相关配置 ---
const previewVisible = ref(false); // 图片预览弹窗
const previewImageUrl = ref(""); // 预览图片URL



// --- 弹窗和表单状态 ---
const visible = ref(false);
const title = ref(""); // 弹窗标题 (通过 #title 插槽实现双标题)
const form = ref({});
const articleFormRef = ref(null);

// 监听 modelValue 变化来控制内部 visible
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      // 弹窗打开时，根据状态设置标题和表单数据
      if (props.isPreview) {
        title.value = "文章详情";
      } else {
        title.value = props.isEdit ? "编辑文章" : "发布文章";
      }
      // 使用 nextTick 确保 DOM 更新后再重置表单和赋值
      nextTick(() => {
        reset(); // 先重置表单
        // 预览或编辑模式下填充数据
        if ((props.isEdit || props.isPreview) && props.articleData) {
          console.log('原始文章数据:', props.articleData);
          form.value = { ...props.articleData }; // 填充数据

          // 处理封面图字段映射：coverImage -> cover
          if (props.articleData.coverImage) {
            form.value.cover = props.articleData.coverImage;
            console.log('设置封面图:', form.value.cover);
          }

          // 处理图片列表字段映射：imagesList -> images
          // if (props.articleData.imagesList) {
          //   try {
          //     // imagesList 是 JSON 字符串，需要解析
          //     const imageArray = JSON.parse(props.articleData.imagesList);
          //     console.log('解析后的图片数组:', imageArray);
          //     if (Array.isArray(imageArray)) {
          //       // 提取 url 字段并转换为逗号分隔的字符串
          //       form.value.images = imageArray.map(item => item.url).join(',');
          //       console.log('设置图片列表:', form.value.images);
          //     }
          //   } catch (error) {
          //     console.error('解析 imagesList 失败:', error);
          //     form.value.images = '';
          //   }
          // }

          // 如果 images 是数组，转换为字符串（兼容处理）
          // if (Array.isArray(form.value.images)) {
          //   form.value.images = form.value.images.join(',');
          // }
          form.value.images = props.articleData.imps;
          console.log('最终表单数据:', form.value);
        }
        // 预览模式下清除校验状态
        if (props.isPreview && articleFormRef.value) {
           articleFormRef.value.clearValidate();
        }
      });
    }
  },
  { immediate: true }
);

// 监听封面图变化，触发验证
watch(
  () => form.value.cover,
  (newVal, oldVal) => {
    console.log('封面图变化:', { oldVal, newVal });
    if (newVal && articleFormRef.value) {
      // 延迟触发验证，确保表单已更新
      nextTick(() => {
        console.log('触发封面图验证');
        articleFormRef.value.validateField("cover");
      });
    }
  }
);

// 表单校验规则 (需要根据实际字段定义)
// 预览模式下不应用规则
const rules = computed(() => props.isPreview ? {} : {
    cover: [
      {
        required: true,
        message: "请上传封面图",
        trigger: "change",
        validator: (_rule, value, callback) => {
          if (!value || value === '') {
            callback(new Error('请上传封面图'));
          } else {
            callback();
          }
        }
      }
    ],
    title: [{ required: true, message: "文章标题不能为空", trigger: "blur" }],
    content: [{ required: true, message: "文章内容不能为空", trigger: "blur" }],
    // images 校验可以根据业务需求添加，例如至少上传一张图片
});

// 表单重置
function reset() {
  form.value = {
    articleId: undefined,
    title: '',
    content: '',
    cover: '', // 封面图URL
    images: '', // 图片URL字符串（逗号分隔）
    // 其他字段...
  };
  if (articleFormRef.value) {
    articleFormRef.value.resetFields(); // 重置校验状态
  }
}



// --- 图片预览处理 ---

// 图片预览 (暂时保留，可能在ImageUpload组件中使用)
// const handlePictureCardPreview = uploadFile => {
//   previewImageUrl.value = uploadFile.url; // url 是 el-upload 内部管理的预览地址
//   previewVisible.value = true;
// };

// --- 弹窗控制 ---

// 取消按钮
function cancel() {
  visible.value = false;
  emit("update:modelValue", false); // 更新父组件的 v-model
}

// 弹窗关闭时的回调
function handleClose() {
  emit("update:modelValue", false); // 确保关闭时同步状态
}

// 提交按钮
function submitForm() {
  // 预览模式下不执行提交
  if (props.isPreview) {
     cancel(); // 直接关闭
     return;
   }

  console.log('提交表单，当前表单数据:', form.value);
  articleFormRef.value.validate((valid, fields) => {
    if (valid) {
      // 处理图片数据
      // const imageUrls = form.value.images
      //   ? form.value.images.split(',').filter(url => url.trim())
      //   : [];

      const postData = {
        title: form.value.title,
        content: form.value.content,
        cover: form.value.cover,
        publishScope: 1, // 默认全市发布
        scopeRange: 0, // 全市为0
        communityId: 0, // 暂时设为0，后续可根据需要调整
        imgList: [],
        videos: [], // 暂时为空数组
        imps: form.value.images.split(','), // 暂时为空数组
        postId: props.isEdit ? form.value.id : undefined
      };

      const apiCall = props.isEdit
        ? updatePost(postData)
        : publishPost(postData);

      apiCall
        .then(() => {
          proxy.$modal.msgSuccess(props.isEdit ? "修改成功" : "发布成功");
          visible.value = false;
          emit("update:modelValue", false); // 关闭弹窗
          emit("submitSuccess"); // 通知父组件提交成功，以便刷新列表
        })
        .catch(() => {
          // 处理API错误
          proxy.$modal.msgError("操作失败");
        });
    } else {
      // 表单验证失败
      console.log('表单验证失败:', fields);
      // 检查具体哪个字段验证失败
      if (fields.cover) {
        proxy.$modal.msgError("请上传封面图");
      } else if (fields.title) {
        proxy.$modal.msgError("请输入文章标题");
      } else if (fields.content) {
        proxy.$modal.msgError("请输入文章内容");
      } else {
        proxy.$modal.msgError("请完善表单信息");
      }
    }
  });
}
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
}

// PC端上传组件样式
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 110px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

// 限制图片列表容器宽度，防止换行混乱
.image-uploader :deep(.el-upload-list--picture-card) {
  gap: 8px; /* 图片间距 */
}

// 调整表单项间距
.el-form-item {
  margin-bottom: 18px; // 调整表单项之间的垂直间距
}

// 调整dialog footer的样式
.dialog-footer {
  text-align: right;
}

// 针对特定上传组件的提示文字样式
.cover-uploader .el-upload__tip,
.image-uploader .el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

// 移动端适配
@media (max-width: 768px) {
  // 移动端上传组件样式调整
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
    line-height: 88px;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 80px;
    height: 80px;
  }

  // 移动端表单项间距调整
  .el-form-item {
    margin-bottom: 15px;
  }

  // 移动端按钮样式
  .dialog-footer {
    text-align: center;
    padding: 15px 0;

    .el-button {
      width: 120px;
      margin: 0 10px;
    }
  }

  // 移动端提示文字样式
  .cover-uploader .el-upload__tip,
  .image-uploader .el-upload__tip {
    font-size: 11px;
    margin-top: 3px;
  }

  // 移动端图片上传列表布局优化
  .image-uploader :deep(.el-upload-list--picture-card) {
    gap: 5px;
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  :deep(.el-upload--picture-card) {
    width: 70px;
    height: 70px;
    line-height: 78px;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 70px;
    height: 70px;
  }

  .dialog-footer {
    .el-button {
      width: 100px;
      margin: 0 5px;
    }
  }
}
</style>

// 全局样式，用于修改 el-dialog 内部结构
<style lang="scss">
.article-dialog {
  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-right: 0; // 覆盖默认 margin
    padding-bottom: 10px;
  }
  .el-dialog__body {
    padding: 20px 25px; // 调整内容区域内边距
  }
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 15px 25px;
  }

  // 移动端全屏模式样式调整
  &.is-fullscreen {
    .el-dialog__header {
      padding: 15px 20px 10px;
    }
    .el-dialog__body {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    .el-dialog__footer {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
    }
  }
}

// 移动端特殊样式
@media (max-width: 768px) {
  .article-dialog {
    .el-dialog__header {
      padding: 12px 15px 8px;
    }
    .el-dialog__body {
      padding: 15px;
    }
    .el-dialog__footer {
      padding: 10px 15px;
    }
  }
}
</style>
