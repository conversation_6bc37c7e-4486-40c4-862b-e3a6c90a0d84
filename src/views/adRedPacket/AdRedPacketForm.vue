<template>
  <el-dialog
    :title="title"
    :model-value="modelValue"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="ad-red-packet-dialog"
    :fullscreen="isMobile"
    @update:modelValue="handleClose"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      label-position="top"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="ad-red-packet-form"
    >
      <!-- 第一行：广告图和宣传语 -->
      <el-row :gutter="isMobile ? 0 : 24">
        <el-col :span="isMobile ? 24 : 12">
          <el-form-item label="广告图" prop="adImg">
            <div class="upload-container">
              <image-upload
                v-model="formData.adImg"
                :limit="1"
                :fileSize="5"
                :fileType="['png', 'jpg', 'jpeg']"
                :isShowTip="false"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="isMobile ? 24 : 12">
          <el-form-item label="宣传语" prop="ad">
            <el-input
              v-model="formData.ad"
              placeholder="请输入..."
              maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：数量、金额、有效期 -->
      <el-row :gutter="isMobile ? 0 : 24">
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="数量" prop="num">
            <el-input
              v-model="formData.num"
              placeholder="20"
              @input="handleNumInput"
            >
              <template #append>人</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="金额（天）" prop="totalAmount">
            <el-input
              v-model="formData.totalAmount"
              placeholder="50.00"
              @input="handleAmountInput"
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="有效期" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="date"
              placeholder="2023-03-16"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：周边公里数和社区 -->
      <el-row :gutter="isMobile ? 0 : 24">
        <el-col :span="isMobile ? 24 : 8">
          <el-form-item label="周边公里数（选填）" prop="kmLimit">
            <el-input
              v-model="formData.kmLimit"
              placeholder="0"
              @input="handleKmInput"
            >
              <template #append>公里</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="isMobile ? 24 : 16">
          <el-form-item label="社区" prop="communityId">
            <el-select
              v-model="formData.communityId"
              placeholder="请选择社区（不选则为全平台）"
              :loading="communityLoading"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="community in communityList"
                :key="community.id"
                :label="community.name"
                :value="community.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="submitForm" class="submit-btn"
          >确认发布</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, onMounted, computed, onUnmounted } from "vue";
import ImageUpload from "@/components/ImageUpload/index.vue"; // 导入图片上传组件
import { createRedBag, getCommunityList } from "@/api/redBag";

const props = defineProps({
  modelValue: Boolean, // 控制弹窗显示/隐藏
});

const emit = defineEmits(["update:modelValue", "submitSuccess"]);

const { proxy } = getCurrentInstance();

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '800px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
  getCommunityListData(); // 加载社区列表
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const title = ref("新增广告红包");
const formRef = ref(null);
const formData = ref(getDefaultFormData());
const communityList = ref([]); // 社区列表
const communityLoading = ref(false); // 社区加载状态

// 默认表单数据
function getDefaultFormData() {
  return {
    adImg: "", // API字段：adImg (string)
    ad: "", // API字段：ad (string) 广告语
    num: "", // API字段：num (integer) 总人数，界面输入为字符串，提交时转换
    totalAmount: "", // API字段：totalAmount (number) 总金额，界面输入为字符串，提交时转换
    endTime: "", // API字段：endTime (string) 结束时间
    kmLimit: "", // API字段：kmLimit (integer) 周边公里数，界面输入为字符串，提交时转换
    communityId: null, // API字段：communityId (integer) 社区Id
  };
}

// 校验规则
const rules = reactive({
  adImg: [{ required: true, message: "请上传广告图", trigger: "change" }],
  ad: [{ required: true, message: "请输入宣传语", trigger: "blur" }],
  num: [
    { required: true, message: "请输入红包数量", trigger: "blur" },
    {
      validator: (_rule, value, callback) => {
        if (!value || value === '') {
          callback(new Error('请输入红包数量'));
        } else if (!/^\d+$/.test(value.toString()) || parseInt(value) < 1) {
          callback(new Error('数量必须为正整数'));
        } else {
          callback();
        }
      },
      trigger: "blur"
    },
  ],
  totalAmount: [
    { required: true, message: "请输入总金额", trigger: "blur" },
    {
      validator: (_rule, value, callback) => {
        if (!value || value === '') {
          callback(new Error('请输入总金额'));
        } else if (!/^\d+(\.\d{1,2})?$/.test(value.toString()) || parseFloat(value) <= 0) {
          callback(new Error('金额必须为正数，最多两位小数'));
        } else {
          callback();
        }
      },
      trigger: "blur"
    },
  ],
  endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  kmLimit: [
    {
      validator: (_rule, value, callback) => {
        if (value !== null && value !== undefined && value !== '') {
          if (!/^\d+$/.test(value.toString()) || parseInt(value) < 0) {
            callback(new Error('公里数必须为非负整数'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    },
  ],
});

// 禁用今天之前的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7; // 减去一天的时间戳，允许选择当天
};

// 处理数量输入（只允许正整数）
function handleNumInput(value) {
  // 移除非数字字符
  const numericValue = value.replace(/[^\d]/g, '');
  // 确保不为空且为正整数，保存为字符串，提交时再转换
  if (numericValue === '' || numericValue === '0') {
    formData.value.num = '';
  } else {
    formData.value.num = numericValue;
  }
}

// 处理金额输入（只允许正数，最多两位小数）
function handleAmountInput(value) {
  // 移除非数字和小数点的字符
  let numericValue = value.replace(/[^\d.]/g, '');

  // 确保只有一个小数点
  const parts = numericValue.split('.');
  if (parts.length > 2) {
    numericValue = parts[0] + '.' + parts.slice(1).join('');
  }

  // 限制小数点后最多两位
  if (parts.length === 2 && parts[1].length > 2) {
    numericValue = parts[0] + '.' + parts[1].substring(0, 2);
  }

  // 确保不以小数点开头
  if (numericValue.startsWith('.')) {
    numericValue = '0' + numericValue;
  }

  formData.value.totalAmount = numericValue;
}

// 处理公里数输入（只允许非负整数）
function handleKmInput(value) {
  // 移除非数字字符
  const numericValue = value.replace(/[^\d]/g, '');
  // 允许为空或非负整数，保存为字符串，提交时再转换
  if (numericValue === '') {
    formData.value.kmLimit = '';
  } else {
    formData.value.kmLimit = numericValue;
  }
}

// 获取社区列表
function getCommunityListData() {
  communityLoading.value = true;
  getCommunityList({
    // 不传分页参数，获取所有数据
  }).then(response => {
    if (response.code === 200) {
      communityList.value = response.data || [];
    }
    communityLoading.value = false;
  }).catch(() => {
    communityLoading.value = false;
  });
}

// 监听弹窗打开，重置表单
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      resetForm();
    }
  }
);

// 组件挂载时获取社区列表 (已在上面的onMounted中处理)

// 重置表单
function resetForm() {
  formData.value = getDefaultFormData();
  if (formRef.value) {
    // 确保 ImageUpload 组件也能被重置，如果它内部没有监听 v-model 清空的话
    // 可能需要手动调用 ImageUpload 的重置方法，或者确保 v-model='' 能触发其内部清空
    formRef.value.resetFields();
  }
}

// 关闭弹窗
function handleClose() {
  emit("update:modelValue", false);
  resetForm(); // 关闭时重置表单
}

// 提交表单
function submitForm() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      const submitData = { ...formData.value };

      // 数据类型转换，确保符合API要求
      // num: integer 类型
      if (submitData.num) {
        submitData.num = parseInt(submitData.num, 10);
      }

      // totalAmount: number 类型
      if (submitData.totalAmount) {
        submitData.totalAmount = parseFloat(submitData.totalAmount);
      }

      // kmLimit: integer 类型
      if (submitData.kmLimit !== null && submitData.kmLimit !== undefined) {
        submitData.kmLimit = parseInt(submitData.kmLimit, 10);
      }

      // communityId: integer 类型，如果没有选择社区，则设为null表示全平台
      if (!submitData.communityId) {
        submitData.communityId = null;
      } else {
        submitData.communityId = parseInt(submitData.communityId, 10);
      }

      console.log("提交的数据:", submitData);

      proxy.$modal.loading("正在提交...");
      createRedBag(submitData)
        .then((response) => {
          proxy.$modal.closeLoading();
          if (response.code === 200) {
            proxy.$modal.msgSuccess("新增成功");
            emit("submitSuccess");
            handleClose();
          }
        })
        .catch(() => {
          proxy.$modal.closeLoading();
        });
    } else {
      console.log("表单校验失败");
      return false;
    }
  });
}
</script>

<style lang="scss" scoped>
.ad-red-packet-dialog {
  :deep(.el-dialog__header) {
    padding: 20px 24px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    font-size: 16px;
    color: #333;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }

  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
    }
  }
}

.ad-red-packet-form {
  :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  :deep(.el-form-item__label) {
    font-weight: 400;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    padding-right: 12px;
  }

  // 上传容器样式
  .upload-container {
    :deep(.el-upload--picture-card) {
      width: 40px;
      height: 40px;
      border: 1px dashed #d9d9d9;
      border-radius: 8px;
      background: #fafafa;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        font-size: 20px;
        color: #8c8c8c;
      }
    }

    :deep(.el-upload-list__item-actions){
      zoom: 0.5;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 40px;
      height: 40px;
      border-radius: 8px;
    }
  }






}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-btn {
    padding: 8px 24px;
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    color: #666;
    font-size: 14px;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  .submit-btn {
    padding: 8px 24px;
    height: 36px;
    border-radius: 6px;
    background: #1890ff;
    border-color: #1890ff;
    font-size: 14px;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .ad-red-packet-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 15px;
    }
  }

  .ad-red-packet-form {
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
      line-height: 36px;
      padding-right: 8px;
    }

    // 移动端上传容器样式调整
    .upload-container {
      :deep(.el-upload--picture-card) {
        width: 60px;
        height: 60px;

        .el-icon {
          font-size: 18px;
        }
      }

      :deep(.el-upload-list--picture-card .el-upload-list__item) {
        width: 60px;
        height: 60px;
      }
    }
  }

  .dialog-footer {
    justify-content: center;
    gap: 15px;

    .cancel-btn,
    .submit-btn {
      width: 120px;
      padding: 8px 16px;
      font-size: 13px;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .ad-red-packet-form {
    .upload-container {
      :deep(.el-upload--picture-card) {
        width: 50px;
        height: 50px;

        .el-icon {
          font-size: 16px;
        }
      }

      :deep(.el-upload-list--picture-card .el-upload-list__item) {
        width: 50px;
        height: 50px;
      }
    }
  }

  .dialog-footer {
    .cancel-btn,
    .submit-btn {
      width: 100px;
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}
</style>
