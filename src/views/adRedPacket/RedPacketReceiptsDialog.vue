<template>
  <el-dialog
    title="领取情况"
    :model-value="modelValue"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="receipts-dialog"
    :fullscreen="isMobile"
    @update:modelValue="handleClose"
    @close="handleClose"
    @open="handleOpen"
  >
    <div v-loading="loading">
      <div class="summary-section">
        <span>领取总人数：<strong>{{ summary.totalRecipients || 0 }}</strong></span>
        <span style="margin-left: 30px;">关联社区数：<strong>{{ summary.communityCount || 0 }}</strong></span>
      </div>

      <el-table :data="receiptsList" style="margin-top: 15px;" :class="{ 'mobile-table': isMobile }">
        <el-table-column label="用户头像" align="center" :width="isMobile ? 60 : 100">
          <template #default>
            <el-avatar :size="isMobile ? 30 : 40" :src="defaultAvatar" />
          </template>
        </el-table-column>
        <el-table-column label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" :min-width="isMobile ? 80 : 120" />
        <el-table-column label="领取金额" align="center" prop="amount" :width="isMobile ? 80 : 100">
           <template #default="scope">
             <span>{{ scope.row.amount?.toFixed(2) }}</span>
           </template>
        </el-table-column>
        <el-table-column v-if="!isMobile" label="社区名称" align="center" prop="communityName" :show-overflow-tooltip="true" />
        <el-table-column label="领取时间" align="center" prop="createdAt" :width="isMobile ? 100 : 160">
          <template #default="scope">
            <span>{{ isMobile ? formatMobileTime(scope.row.createdAt) : parseTime(scope.row.createdAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="isMobile ? 60 : 100">
          <template #default="scope">
            <el-button link type="primary" @click="handleChat(scope.row)" :size="isMobile ? 'small' : 'default'">聊天</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getReceipts"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 30, 50]"
      />
    </div>
     <!-- <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关 闭</el-button>
        </div>
      </template> -->
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed, onMounted, onUnmounted } from 'vue';
import { getRedBagReceiveUserList } from "@/api/redBag";
import defaultAvatar from '@/assets/images/profile.jpg'; // 默认头像路径

const props = defineProps({
  modelValue: Boolean, // 控制弹窗显示/隐藏
  redPacketId: [Number, String], // 当前广告红包的ID
});

const emit = defineEmits(['update:modelValue']);

const { proxy } = getCurrentInstance();
const { parseTime } = proxy;

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '800px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// 移动端时间格式化
const formatMobileTime = (time) => {
  if (!time) return '';
  const date = new Date(time);
  return `${date.getMonth() + 1}/${date.getDate()}`;
};

const loading = ref(false);
const summary = ref({ totalRecipients: 0, communityCount: 0 }); // 汇总信息
const receiptsList = ref([]); // 领取记录列表
const total = ref(0); // 总条数

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 获取领取记录列表
function getReceipts() {
  if (!props.redPacketId) return;
  loading.value = true;

  const params = {
    ...queryParams,
    redBagId: props.redPacketId
  };

  getRedBagReceiveUserList(params).then(response => {
    if (response.code === 200) {
      receiptsList.value = response.data.list;
      total.value = response.data.total;
      // 计算汇总信息
      summary.value = {
        totalRecipients: response.data.total,
        communityCount: new Set(response.data.list.map(item => item.communityName)).size
      };
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}




// 监听弹窗打开事件
function handleOpen() {
    // 重置分页并获取数据
    queryParams.pageNum = 1;
    getReceipts();
}


// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false);
  // 清空数据和重置状态可以在关闭时或打开时进行，这里选择在打开时重置
  // receiptsList.value = [];
  // total.value = 0;
  // summary.value = { totalRecipients: 0, communityCount: 0 };
}

// 聊天按钮操作
function handleChat(row) {
  // TODO: 实现跳转到聊天或打开聊天窗口的逻辑
  proxy.$modal.msgSuccess(`与用户 ${row.userName} 开始聊天 (模拟)`);
  console.log("开始聊天:", row);
}

// 监听 redPacketId 变化，如果弹窗已打开则重新加载数据
// watch(() => props.redPacketId, (newId, oldId) => {
//   if (newId && props.modelValue) { // 仅当弹窗可见且ID有效时
//     queryParams.pageNum = 1; // 重置分页
//     getReceipts();
//   }
// });

// 监听弹窗状态变化，打开时加载数据 (替代上面的watch)
// watch(() => props.modelValue, (newVal) => {
//   if (newVal && props.redPacketId) {
//     queryParams.pageNum = 1;
//     getReceipts();
//   }
// });

</script>

<style lang="scss" scoped>
.receipts-dialog {
  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.summary-section {
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;

  strong {
    color: #303133;
    margin: 0 5px;
  }
}

.dialog-footer {
  text-align: right;
}

// 移动端适配
@media (max-width: 768px) {
  .receipts-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
  }

  .summary-section {
    font-size: 13px;
    margin-bottom: 12px;

    // 移动端汇总信息垂直布局
    display: flex;
    flex-direction: column;
    gap: 5px;

    span {
      display: block;
    }
  }

  // 移动端表格样式调整
  .mobile-table {
    :deep(.el-table__header) {
      th {
        padding: 8px 4px;
        font-size: 12px;
      }
    }

    :deep(.el-table__body) {
      td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }

    :deep(.el-button--small) {
      padding: 4px 8px;
      font-size: 11px;
    }
  }

  // 移动端分页样式调整
  :deep(.el-pagination) {
    justify-content: center;
    margin-top: 15px;

    .el-pagination__total,
    .el-pagination__jump {
      display: none;
    }

    .el-pagination__sizes {
      display: none;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .summary-section {
    font-size: 12px;

    strong {
      margin: 0 3px;
    }
  }

  .mobile-table {
    :deep(.el-table__header) {
      th {
        padding: 6px 2px;
        font-size: 11px;
      }
    }

    :deep(.el-table__body) {
      td {
        padding: 6px 2px;
        font-size: 11px;
      }
    }

    :deep(.el-avatar) {
      width: 25px !important;
      height: 25px !important;
    }

    :deep(.el-button--small) {
      padding: 2px 6px;
      font-size: 10px;
    }
  }
}
</style>