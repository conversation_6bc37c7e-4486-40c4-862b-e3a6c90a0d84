<template>
  <el-dialog
    title="红包用户列表"
    :model-value="modelValue"
    :width="dialogWidth"
    :top="dialogTop"
    append-to-body
    class="red-bag-users-dialog"
    :fullscreen="isMobile"
    @update:modelValue="handleClose"
    @close="handleClose"
  >
    <el-table v-loading="loading" :data="usersList" :class="{ 'mobile-table': isMobile }">
      <el-table-column label="用户头像" align="center" :width="isMobile ? 60 : 100">
        <template #default>
          <el-avatar :size="isMobile ? 30 : 40" :src="defaultAvatar" />
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" :min-width="isMobile ? 80 : 120" />
      <el-table-column v-if="!isMobile" label="社区名称" align="center" prop="communityName" :show-overflow-tooltip="true" />
      <el-table-column label="剩余聊天时长（天）" align="center" :width="isMobile ? 100 : 160">
        <template #default="scope">
          <span style="color: #ff6b35;">{{ isMobile ? calculateRemainingDays(scope.row.createdAt) + '天' : calculateRemainingDays(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="isMobile ? 60 : 100">
        <template #default>
          <el-button link type="primary" :size="isMobile ? 'small' : 'default'">踢出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getUsers"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, watch, computed, onMounted, onUnmounted } from 'vue';
import { getRedBagReceiveUserList } from "@/api/redBag";
import defaultAvatar from '@/assets/images/profile.jpg'; // 默认头像路径

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  redBagId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['update:modelValue']);

const { proxy } = getCurrentInstance();

// --- 响应式设计相关 ---
const isMobile = ref(false);
const dialogWidth = computed(() => isMobile.value ? '95%' : '800px');
const dialogTop = computed(() => isMobile.value ? '2vh' : '15vh');

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

// 状态变量
const loading = ref(false);
const usersList = ref([]);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  redBagId: null
});

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false);
}

// 获取用户列表
function getUsers() {
  if (!props.redBagId) return;
  
  loading.value = true;
  queryParams.redBagId = props.redBagId;
  
  getRedBagReceiveUserList(queryParams).then(response => {
    if (response.code === 200) {
      usersList.value = response.data.list;
      total.value = response.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 计算剩余天数（这里简单模拟，实际应该根据业务逻辑计算）
function calculateRemainingDays(createdAt) {
  if (!createdAt) return 'N/A';
  
  // 简单模拟：假设红包有效期是30天
  const createDate = new Date(createdAt);
  const expireDate = new Date(createDate.getTime() + 30 * 24 * 60 * 60 * 1000);
  const now = new Date();
  const diff = expireDate.getTime() - now.getTime();
  
  if (diff <= 0) {
    return 0;
  }
  
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
  return days;
}

// 监听弹窗打开，获取数据
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.redBagId) {
      // 重置分页
      queryParams.pageNum = 1;
      getUsers();
    }
  }
);

// 监听红包ID变化
watch(
  () => props.redBagId,
  (newVal) => {
    if (newVal && props.modelValue) {
      // 重置分页
      queryParams.pageNum = 1;
      getUsers();
    }
  }
);
</script>

<style lang="scss" scoped>
.red-bag-users-dialog {
  :deep(.el-dialog__header) {
    padding: 20px 24px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    font-size: 16px;
    color: #333;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  // 移动端全屏模式样式调整
  &.is-fullscreen {
    :deep(.el-dialog__header) {
      padding: 15px 20px 10px;
    }
    :deep(.el-dialog__body) {
      padding: 15px 20px;
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px 15px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
      border-top: 1px solid #f0f0f0;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .red-bag-users-dialog {
    :deep(.el-dialog__header) {
      padding: 12px 15px 8px;
      font-size: 15px;
    }
    :deep(.el-dialog__body) {
      padding: 15px;
    }
  }

  // 移动端表格样式调整
  .mobile-table {
    :deep(.el-table__header) {
      th {
        padding: 8px 4px;
        font-size: 12px;
      }
    }

    :deep(.el-table__body) {
      td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }

    :deep(.el-button--small) {
      padding: 4px 8px;
      font-size: 11px;
    }
  }

  // 移动端分页样式调整
  :deep(.el-pagination) {
    justify-content: center;
    margin-top: 15px;

    .el-pagination__total,
    .el-pagination__jump {
      display: none;
    }

    .el-pagination__sizes {
      display: none;
    }
  }
}

// 超小屏幕适配 (小于480px)
@media (max-width: 480px) {
  .mobile-table {
    :deep(.el-table__header) {
      th {
        padding: 6px 2px;
        font-size: 11px;
      }
    }

    :deep(.el-table__body) {
      td {
        padding: 6px 2px;
        font-size: 11px;
      }
    }

    :deep(.el-avatar) {
      width: 25px !important;
      height: 25px !important;
    }

    :deep(.el-button--small) {
      padding: 2px 6px;
      font-size: 10px;
    }
  }
}
</style>
