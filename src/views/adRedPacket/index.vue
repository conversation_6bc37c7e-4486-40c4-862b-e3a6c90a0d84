<template>
  <div class="app-container">
    <!-- 广告红包管理页面 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['marketing:adRedPacket:add']"
        >新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="redPacketList">
      <el-table-column label="广告图" align="center" prop="adImg" width="100">
        <template #default="scope">
          <el-image style="width: 60px; height: 60px" :src="scope.row.adImg || defaultImage" fit="cover"></el-image>
        </template>
      </el-table-column>
      <el-table-column label="宣传语" align="left" prop="ad" :show-overflow-tooltip="true" />
      <el-table-column label="单个金额" align="center" prop="amount" width="100">
         <template #default="scope">
           <span>¥{{ scope.row.amount?.toFixed(2) }}</span>
         </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="num" width="80" />
      <el-table-column label="社区" align="center" prop="communityName" :show-overflow-tooltip="true" />
      <el-table-column label="剩余有效期" align="center" prop="endTime" width="120">
        <template #default="scope">
          <span>{{ formatRemainingTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="周边公里数" align="center" prop="kmLimit" width="110">
        <template #default="scope">
          <span>{{ scope.row.kmLimit ? scope.row.kmLimit + ' km' : '无限制' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="260">
        <template #default="scope">
          <el-button link type="info" @click="handleRedBagUsers(scope.row)" v-hasPermi="['marketing:adRedPacket:users']">红包用户</el-button>
          <el-button link type="primary" @click="handleReceipts(scope.row)" v-hasPermi="['marketing:adRedPacket:receipts']">领取情况</el-button>
          <!-- 根据状态显示上架或下架按钮 -->
          <el-button
            v-if="scope.row.status === 1"
            link
            type="warning"
            @click="handleTakeDown(scope.row)"
            v-hasPermi="['marketing:adRedPacket:takedown']"
          >
            下架
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            link
            type="success"
            @click="handleTakeUp(scope.row)"
            v-hasPermi="['marketing:adRedPacket:takeup']"
          >
            上架
          </el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['marketing:adRedPacket:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增广告红包表单 -->
    <ad-red-packet-form
      v-model="formDialogVisible"
      @submitSuccess="getList"
    />

    <!-- 红包领取情况弹窗 -->
    <red-packet-receipts-dialog
      v-model="receiptsDialogVisible"
      :red-packet-id="currentRedPacketId"
    />

    <!-- 红包用户列表弹窗 -->
    <red-bag-users-dialog
      v-model="redBagUsersDialogVisible"
      :red-bag-id="currentRedPacketId"
    />

  </div>
</template>

<script setup name="AdRedPacket">
import { ref, reactive, onMounted, onActivated, getCurrentInstance } from 'vue';
import AdRedPacketForm from './AdRedPacketForm.vue';
import RedPacketReceiptsDialog from './RedPacketReceiptsDialog.vue';
import RedBagUsersDialog from './RedBagUsersDialog.vue';
import { listRedBag, deleteRedBag, updateRedBagStatus } from "@/api/redBag";
import defaultImage from '@/assets/images/profile.jpg'; // 默认图片路径

const { proxy } = getCurrentInstance();
const { parseTime } = proxy; // 假设 parseTime 在全局挂载

// 加载状态
const loading = ref(true);
// 总条数
const total = ref(0);
// 广告红包表格数据
const redPacketList = ref([]);
// 新增表单弹窗可见性
const formDialogVisible = ref(false);
// 领取情况弹窗可见性
const receiptsDialogVisible = ref(false);
// 红包用户弹窗可见性
const redBagUsersDialogVisible = ref(false);
// 当前操作的红包ID
const currentRedPacketId = ref(null);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  latitude: null, // 纬度
  longitude: null, // 经度
  merchantId: null // 商家id
});

/** 查询红包列表 */
function getList() {
  loading.value = true;
  listRedBag(queryParams).then(response => {
    // 根据接口文档，成功时code为200，数据在data字段中
    if (response.code === 200) {
      // 直接使用后台返回的数据，不做字段映射
      redPacketList.value = response.data.list.map(item => ({
        ...item,
        // 只处理需要默认值的字段
        communityName: item.communityName || '全平台'
      }));
      total.value = response.data.total;
    }
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 新增按钮操作 */
function handleAdd() {
  formDialogVisible.value = true;
}

/** 红包领取情况按钮操作 */
function handleReceipts(row) {
  currentRedPacketId.value = row.id; // 假设列表项有id字段
  receiptsDialogVisible.value = true;
}

/** 红包用户按钮操作 */
function handleRedBagUsers(row) {
  currentRedPacketId.value = row.id;
  redBagUsersDialogVisible.value = true;
}

/** 下架按钮操作 */
function handleTakeDown(row) {
  proxy.$modal.confirm('是否确认下架宣传语为"' + row.ad + '"的广告红包？').then(function() {
    // 调用红包状态更新接口，status: 2表示下架
    return updateRedBagStatus({
      redBagId: row.id,
      status: 2
    });
  }).then((response) => {
    if (response.code === 200) {
      getList(); // 重新加载列表
      proxy.$modal.msgSuccess("下架成功");
    }
  }).catch(() => {});
}

/** 上架按钮操作 */
function handleTakeUp(row) {
  proxy.$modal.confirm('是否确认上架宣传语为"' + row.ad + '"的广告红包？').then(function() {
    // 调用红包状态更新接口，status: 1表示上架
    return updateRedBagStatus({
      redBagId: row.id,
      status: 1
    });
  }).then((response) => {
    if (response.code === 200) {
      getList(); // 重新加载列表
      proxy.$modal.msgSuccess("上架成功");
    }
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除宣传语为"' + row.ad + '"的广告红包？').then(function() {
    // 调用删除红包接口
    return deleteRedBag(row.id);
  }).then((response) => {
    if (response.code === 200) {
      getList(); // 重新加载列表
      proxy.$modal.msgSuccess("删除成功");
    }
  }).catch(() => {});
}

/** 格式化剩余时间 */
function formatRemainingTime(expireTime) {
  if (!expireTime) return 'N/A';
  const now = new Date();
  const expiry = new Date(expireTime);
  const diff = expiry.getTime() - now.getTime();

  if (diff <= 0) {
    return '已过期';
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  // 返回更易读的格式，例如 "DD天HH:MM:SS"
  // return `${days}天 ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  // 或者根据图片显示，只显示时分秒倒计时，如果超过一天则显示天数
   if (days > 0) {
       return `${days}天`; // 如果需要更精确可以加上时分秒
   } else {
       return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
   }
}

onMounted(() => {
  getList(); // 组件挂载后获取列表数据
});

// keep-alive 组件激活时重新获取数据
onActivated(() => {
  getList(); // 每次进入页面都获取最新数据
});

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 10px;
}
/* 可选：如果需要固定表格高度 */
.el-table {
  height: calc(100vh - 220px) !important;
} 
</style>