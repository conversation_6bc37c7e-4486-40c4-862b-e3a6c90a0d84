import { login, logout, getInfo, getMerchantInfo } from "@/api/login";
import { getToken, setToken, removeToken, getIMInfo, setIMInfo, removeIMInfo } from "@/utils/auth";
import { isHttp, isEmpty } from "@/utils/validate";
import defAva from "@/assets/images/profile.jpg";

const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    id: "",
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
    // IM相关信息 - 从浏览器存储中恢复
    imInfo: getIMInfo(),
    // 商家信息
    merchantInfo: {
      id: null,
      uid: null,
      name: "",
      slogan: "",
      description: "",
      coverImage: "",
      adUrl: "",
      logo: "",
      longitude: 0,
      latitude: 0,
      distance: 0,
      cityId: null,
      address: "",
      isMember: 0,
      isJoin: 0,
      memberRedBagAmount: 0
    }
  }),
  actions: {
    // 登录
    login(userInfo) {
      // 从 userInfo 中获取 mobile, password 和 loginType
      const mobile = userInfo.mobile.trim();
      const password = userInfo.password;
      const loginType = userInfo.loginType;
      // 注意：新的登录接口不再需要 code 和 uuid
      return new Promise((resolve, reject) => {
        // 调用新的登录接口，假设 login API 已更新为接受 (loginType, mobile, password)
        // 或者根据实际的 api/login.js 中的 login 函数签名调整
        login({ loginType, mobile, password })
          .then((res) => {
            // 假设 login API 接受一个对象
            console.log(res, "请求返回参数");
            // 根据新的返回结果结构更新 token 和用户信息
            // "acceptToken": "e-A",
            // "netToken": "aadb7329fc4c1bd8495f52dc54cd621d",
            // "userInfo": { ... }
            // 假设 res.data 包含 acceptToken 或 netToken 作为主 token
            // 以及 userInfo
            if (res.data && (res.data.acceptToken || res.data.netToken)) {
              const tokenToSet = res.data.acceptToken || res.data.netToken;
              setToken(tokenToSet);
              this.token = tokenToSet;

              // 如果返回了 userInfo，可以考虑在这里也更新一部分用户信息到 store
              if (res.data.userInfo) {
                // this.permissions = ["*:*:*"];
                // this.roles = ["admin"];
                this.id = res.data.userInfo.id;
                this.name = res.data.userInfo.nickname; // 使用 nickname 作为 name
                this.avatar = res.data.userInfo.avatar || defAva; // 处理 avatar

                // 存储IM相关信息
                this.imInfo.account = String(res.data.userInfo.id); // 确保是字符串
                this.imInfo.token = res.data.netToken || "";
                this.imInfo.initialized = false; // 重置初始化状态

                // 将IM信息加密存储到浏览器
                setIMInfo(this.imInfo);

                // 其他用户信息可以按需存储
              }
              resolve();
            } else {
              // 如果 token 结构不符合预期，则 reject
              reject(new Error(res.message || "登录失败，返回数据格式不正确"));
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        this.roles = ["admin"];
        this.permissions = ["*:*:*"];
        // 同时获取商家信息
        this.getMerchantInfo().catch(error => {
          console.warn('获取商家信息失败:', error);
        });
        resolve(true);
        // getInfo()
        //   .then(res => {
        //     const user = res.data;
        //     let avatar = user.avatar || "";
        //     if (!isHttp(avatar)) {
        //       avatar = isEmpty(avatar)
        //         ? defAva
        //         : import.meta.env.VITE_APP_BASE_API + avatar;
        //     }
        //     // if (res.roles && res.roles.length > 0) {
        //     //   // 验证返回的roles是否是一个非空数组
        //     //   this.roles = res.roles;
        //     //   this.permissions = res.permissions;
        //     // } else {
        //     //   this.roles = ["ROLE_DEFAULT"];
        //     // }
        //     this.roles = ["admin"];
        //     this.permissions = ["*:*:*"];
        //     this.id = user.userId;
        //     this.name = user.userName;
        //     this.avatar = avatar;
        //     resolve(res);
        //   })
        //   .catch(error => {
        //     reject(error);
        //   });
      });
    },
    // 获取商家信息
    getMerchantInfo() {
      return new Promise((resolve, reject) => {
        getMerchantInfo()
          .then(res => {
            if (res.code === 200) {
              this.merchantInfo = res.data;
              resolve(res.data);
            } else {
              reject(res);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = "";
            this.roles = [];
            this.permissions = [];
            // 清除IM信息
            this.imInfo = {
              account: "",
              token: "",
              initialized: false
            };
            removeToken();
            removeIMInfo(); // 清除浏览器中的IM信息
            resolve();
          })
          .catch((error) => {
            // 如果是401未授权错误，也直接清除本地状态并退出
            if (error && (error.message === "未授权" || error.code === 401)) {
              this.token = "";
              this.roles = [];
              this.permissions = [];
              // 清除IM信息
              this.imInfo = {
                account: "",
                token: "",
                initialized: false
              };
              removeToken();
              removeIMInfo(); // 清除浏览器中的IM信息
              resolve(); // 直接resolve，不抛出错误
            } else {
              reject(error);
            }
          });
      });
    },
    // 设置IM初始化状态
    setIMInitialized(status) {
      this.imInfo.initialized = status;
      // 更新浏览器存储中的状态（但不重新存储敏感信息）
      if (status && this.imInfo.account && this.imInfo.token) {
        setIMInfo(this.imInfo);
      }
    },
  },
});

export default useUserStore;
