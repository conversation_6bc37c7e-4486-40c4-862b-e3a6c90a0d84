import COS from 'cos-js-sdk-v5'
import { getCosCredential } from '@/api/tencentCos'

/**
 * 腾讯云COS上传工具类
 */
class CosUploader {
  constructor() {
    this.cos = null
    this.credentials = null
    this.credentialsExpireTime = null
    this.isGettingCredentials = false // 防止重复获取凭证
  }

  /**
   * 初始化COS实例
   * @param {Object} credentials - 临时凭证信息
   */
  initCos(credentials) {
    this.credentials = credentials
    // 设置凭证过期时间（提前5分钟过期以确保安全）
    this.credentialsExpireTime = Date.now() + (2 * 60 * 60 * 1000) - (5 * 60 * 1000) // 2小时减去5分钟
    this.cos = new COS({
      SecretId: credentials.tmpSecretId,
      SecretKey: credentials.tmpSecretKey,
      SecurityToken: credentials.sessionToken,
    })
  }

  /**
   * 检查凭证是否过期
   * @returns {boolean} 是否过期
   */
  isCredentialsExpired() {
    return !this.credentialsExpireTime || Date.now() >= this.credentialsExpireTime
  }

  /**
   * 获取临时凭证并初始化COS
   * @param {string} fileName - 文件名
   * @returns {Promise<Object>} 返回凭证信息
   */
  async getCredentialsAndInit(fileName) {
    // 如果正在获取凭证，等待完成
    if (this.isGettingCredentials) {
      while (this.isGettingCredentials) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return this.credentials
    }

    try {
      this.isGettingCredentials = true
      const response = await getCosCredential(fileName)
      if (response.code === 200 && response.success) {
        this.initCos(response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取临时凭证失败')
      }
    } catch (error) {
      console.error('获取COS凭证失败:', error)
      throw error
    } finally {
      this.isGettingCredentials = false
    }
  }

  /**
   * 上传文件到腾讯云COS
   * @param {File} file - 要上传的文件
   * @param {Function} onProgress - 上传进度回调函数
   * @returns {Promise<string>} 返回文件的完整URL
   */
  async uploadFile(file, onProgress) {
    try {
      // 如果没有初始化COS或凭证已过期，先获取凭证并初始化
      if (!this.cos || !this.credentials || this.isCredentialsExpired()) {
        console.log('获取新的COS凭证...')
        await this.getCredentialsAndInit(file.name)
      }

      // 生成文件路径，使用时间戳和随机数避免重名
      const timestamp = Date.now()
      const randomStr = Math.random().toString(36).substring(2, 8)
      const fileExtension = file.name.substring(file.name.lastIndexOf('.'))
      const fileName = `${timestamp}_${randomStr}${fileExtension}`
      
      // 使用后端返回的路径，如果没有则使用默认路径
      const filePath = this.credentials.path ? 
        `${this.credentials.path}/${fileName}` : 
        `uploads/${fileName}`

      return new Promise((resolve, reject) => {
        this.cos.uploadFile({
          Bucket: this.credentials.bucket,
          Region: this.credentials.region,
          Key: filePath,
          Body: file,
          onProgress: (progressData) => {
            if (onProgress) {
              onProgress({
                percent: Math.round(progressData.percent * 100),
                loaded: progressData.loaded,
                total: progressData.total
              })
            }
          }
        }, (err, data) => {
          if (err) {
            console.error('COS上传失败:', err)
            // 如果是凭证相关错误，清除当前凭证以便下次重新获取
            if (err.code === 'InvalidAccessKeyId' || err.code === 'SignatureDoesNotMatch' || err.code === 'TokenExpired') {
              console.log('凭证已过期，清除当前凭证')
              this.cos = null
              this.credentials = null
              this.credentialsExpireTime = null
            }
            reject(err)
          } else {
            // 返回完整的文件URL
            const fileUrl = this.credentials.host ?
              `${this.credentials.host}/${filePath}` :
              `https://${data.Location}`
            resolve(fileUrl)
          }
        })
      })
    } catch (error) {
      console.error('上传文件失败:', error)
      throw error
    }
  }

  /**
   * 批量上传文件
   * @param {FileList|Array} files - 要上传的文件列表
   * @param {Function} onProgress - 上传进度回调函数
   * @param {Function} onSingleComplete - 单个文件上传完成回调
   * @returns {Promise<Array>} 返回所有文件的URL数组
   */
  async uploadFiles(files, onProgress, onSingleComplete) {
    const fileArray = Array.from(files)
    const results = []
    
    for (let i = 0; i < fileArray.length; i++) {
      const file = fileArray[i]
      try {
        const url = await this.uploadFile(file, (progress) => {
          if (onProgress) {
            onProgress({
              currentFile: i + 1,
              totalFiles: fileArray.length,
              fileName: file.name,
              ...progress
            })
          }
        })
        
        results.push({
          name: file.name,
          url: url,
          success: true
        })
        
        if (onSingleComplete) {
          onSingleComplete(file, url, i + 1, fileArray.length)
        }
      } catch (error) {
        results.push({
          name: file.name,
          error: error.message,
          success: false
        })
        
        if (onSingleComplete) {
          onSingleComplete(file, null, i + 1, fileArray.length, error)
        }
      }
    }
    
    return results
  }
}

// 创建单例实例
const cosUploader = new CosUploader()

/**
 * 上传单个文件到腾讯云COS
 * @param {File} file - 要上传的文件
 * @param {Function} onProgress - 上传进度回调函数
 * @returns {Promise<string>} 返回文件URL
 */
export function uploadFileToCos(file, onProgress) {
  return cosUploader.uploadFile(file, onProgress)
}

/**
 * 批量上传文件到腾讯云COS
 * @param {FileList|Array} files - 要上传的文件列表
 * @param {Function} onProgress - 上传进度回调函数
 * @param {Function} onSingleComplete - 单个文件上传完成回调
 * @returns {Promise<Array>} 返回所有文件的结果数组
 */
export function uploadFilesToCos(files, onProgress, onSingleComplete) {
  return cosUploader.uploadFiles(files, onProgress, onSingleComplete)
}

export default cosUploader
