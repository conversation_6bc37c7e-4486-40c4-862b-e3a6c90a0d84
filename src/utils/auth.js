import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const IMInfoKey = 'IM-Info'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

// 简单的Base64编码（用于基本的数据混淆，不是真正的加密）
function encodeBase64(text) {
  try {
    // 先进行URI编码处理中文字符，然后Base64编码
    return btoa(encodeURIComponent(text))
  } catch (error) {
    console.error('Base64编码失败:', error)
    return null
  }
}

// 简单的Base64解码
function decodeBase64(encodedText) {
  try {
    // 先Base64解码，然后URI解码
    return decodeURIComponent(atob(encodedText))
  } catch (error) {
    console.error('Base64解码失败:', error)
    return null
  }
}

// IM信息存储相关函数
export function getIMInfo() {
  try {
    const encryptedInfo = Cookies.get(IMInfoKey)
    if (!encryptedInfo) {
      return {
        account: '',
        token: '',
        initialized: false
      }
    }

    const decryptedInfo = decodeBase64(encryptedInfo)
    if (!decryptedInfo) {
      return {
        account: '',
        token: '',
        initialized: false
      }
    }

    return JSON.parse(decryptedInfo)
  } catch (error) {
    console.warn('获取IM信息失败:', error)
    return {
      account: '',
      token: '',
      initialized: false
    }
  }
}

export function setIMInfo(imInfo) {
  try {
    const infoToStore = {
      account: imInfo.account || '',
      token: imInfo.token || '',
      initialized: false // 存储时重置初始化状态
    }

    const encryptedInfo = encodeBase64(JSON.stringify(infoToStore))
    if (!encryptedInfo) {
      console.error('编码IM信息失败')
      return false
    }

    return Cookies.set(IMInfoKey, encryptedInfo, { expires: 7 }) // 7天过期
  } catch (error) {
    console.error('存储IM信息失败:', error)
    return false
  }
}

export function removeIMInfo() {
  return Cookies.remove(IMInfoKey)
}
