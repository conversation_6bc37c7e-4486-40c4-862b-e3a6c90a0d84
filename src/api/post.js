import request from '@/utils/request'

// 查询帖子列表
export function listPost(query) {
  return request({
    url: '/post/list',
    method: 'post',
    data: query
  })
}

// 发布帖子
export function publishPost(data) {
  return request({
    url: '/post/publish',
    method: 'post',
    data: data
  })
}

// 删除帖子
export function deletePost(data) {
  return request({
    url: '/post/delete',
    method: 'post',
    data: data
  })
}

// 更新帖子
export function updatePost(data) {
  return request({
    url: '/post/update',
    method: 'post',
    data: data
  })
}