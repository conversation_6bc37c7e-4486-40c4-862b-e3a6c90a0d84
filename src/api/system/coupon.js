import request from '@/utils/request'

// 查询商家优惠券列表
export function listCoupon(query) {
  return request({
    url: '/coupons/list',
    method: 'get',
    params: {
        ...query,
        type: query.type === 'cash' ? 1 : (query.type === 'discount' ? 2 : query.type) // 映射类型
    }
  })
}

// 删除优惠券
export function delCoupon(couponsId) {
  return request({
    url: '/coupons/delete',
    method: 'post',
    params: { couponsId }
  })
}

// 上架/下架优惠券
export function updateCouponStatus(data) {
  return request({
    url: '/coupons/status',
    method: 'post',
    data: data
  })
}

// 获取优惠券领取情况
export function getCouponReceiveStatus(couponsId, status) {
  return request({
    url: '/coupons/receiveStatus',
    method: 'get',
    params: { couponsId, status }
  })
}
// 创建优惠券
export function createCoupon(data) {
  return request({
    url: '/coupons/create',
    method: 'post',
    data: data
  })
}

// 更新优惠券
export function updateCoupon(data) {
  return request({
    url: '/coupons/update',
    method: 'post', // 或者 'put'
    data: data
  })
}
