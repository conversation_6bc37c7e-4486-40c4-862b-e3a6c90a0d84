import request from '@/utils/request'

// 查询红包列表
export function listRedBag(query) {
  return request({
    url: '/redBag/list',
    method: 'get',
    params: query
  })
}

// 创建红包
export function createRedBag(data) {
  return request({
    url: '/redBag/create',
    method: 'post',
    data: data
  })
}

// 删除红包
export function deleteRedBag(redBagId) {
  return request({
    url: '/redBag/delete',
    method: 'post',
    params: { redBagId }
  })
}

// 红包上架/下架
export function updateRedBagStatus(data) {
  return request({
    url: '/redBag/upOrDown',
    method: 'post',
    data: data
  })
}

// 查询红包领取情况
export function getRedBagReceiveList(query) {
  return request({
    url: '/redBag/receive/list',
    method: 'get',
    params: query
  })
}

// 查询红包领取用户列表
export function getRedBagReceiveUserList(query) {
  return request({
    url: '/redBag/receive/user/list',
    method: 'get',
    params: query
  })
}

// 获取社区列表
export function getCommunityList(query) {
  return request({
    url: '/community/merchant',
    method: 'get',
    params: query
  })
}
