import request from '@/utils/request'

// 搜索核销码
export function searchWriteOff(writeOffCode) {
  return request({
    url: '/writeOff/search',
    method: 'get',
    params: { writeOffCode }
  })
}

// 获取核销码列表
export function listWriteOff(query) {
  return request({
    url: '/writeOff/list',
    method: 'get',
    params: query
  })
}

// 确认核销
export function confirmWriteOff(data) {
  return request({
    url: '/writeOff/confirm',
    method: 'post',
    data: data
  })
}
