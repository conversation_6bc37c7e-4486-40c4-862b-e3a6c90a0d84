import request from '@/utils/request'

// 登录方法
export function login(userInfo) {
  return request({
    url: 'acc/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: userInfo
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/user/getUserInfo',
    method: 'post'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/acc/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

export function getMerchantInfo(){
  return request({
    url: '/merchant/info',
    method: 'get',
  })
}

// 发送短信验证码
export function sendSmsCode(mobile, scene = 2) {
  return request({
    url: '/acc/sms',
    method: 'post',
    data: {
      mobile,
      scene // 1-登录/注册, 2-重置密码, 3-换绑手机, 4-注销账号, 5-提现
    }
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/acc/setPss',
    method: 'post',
    data: data
  })
}

// 更新商家信息
export function updateMerchantInfo(data) {
  return request({
    url: '/merchant/update',
    method: 'post',
    data: data
  })
}

// 更新商家税号信息
export function updateMerchantTaxNumber(data) {
  return request({
    url: '/merchant/updateTaxNumber',
    method: 'post',
    data: data
  })
}