import request from '@/utils/request'

// 查询会员列表
export function listMember(query) {
  return request({
    url: '/member/query',
    method: 'post',
    data: query
  })
}

// 获取系统配置
export function getSystemConfig() {
  return request({
    url: '/sys/config',
    method: 'get'
  })
}

// 会员购买
export function buyMember(memberNum) {
  return request({
    url: '/member/buyMember',
    method: 'get',
    params: { memberNum }
  })
}

// 查询可用会员数量
export function queryMemberNum() {
  return request({
    url: '/member/queryMemberNum',
    method: 'get'
  })
}

// 查询会员详情信息
export function queryMemberById(memberId) {
  return request({
    url: '/member/queryById',
    method: 'get',
    params: { memberId }
  })
}
