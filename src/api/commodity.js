import request from '@/utils/request'

// 查询商品列表
export function listCommodity(data) {
  return request({
    url: '/goods/list',
    method: 'post',
    data: data
  })
}

// 查询拼团商品列表
export function listGroupSharingGoods(data) {
  return request({
    url: '/groupSharing/list',
    method: 'post',
    data: data
  })
}

// 查询拼团订单列表
export function listGroupSharingOrders(query) {
  return request({
    url: '/groupSharing/queryOrder',
    method: 'get',
    params: query
  })
}

// 查询拼团用户列表
export function listGroupSharingUsers(query) {
  return request({
    url: '/groupSharing/queryUser',
    method: 'get',
    params: query
  })
}
// 更新商品状态
export function updateCommodityStatus(data) {
  return request({
    url: '/goods/status',
    method: 'post',
    data: data
  })
}
// 新增商品
export function addCommodity(data) {
  return request({
    url: '/goods/save',
    method: 'post',
    data: data
  })
}

// 修改商品
export function updateCommodity(data) {
  return request({
    url: '/goods/update',
    method: 'post',
    data: data
  })
}

// 删除商品
export function deleteCommodity(goodsId) {
  return request({
    url: '/goods/delete',
    method: 'get',
    params: { goodsId }
  })
}